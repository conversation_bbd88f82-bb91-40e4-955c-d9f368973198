# Next.js App Router Patterns - Zenera Implementation

## 🎯 Overview

Zenera sử dụng Next.js 15.x với App Router, yêu cầu phân biệt rõ ràng giữa Server Components và Client Components để tối ưu performance và tránh lỗi runtime.

## 📋 Client vs Server Components Strategy

### **Server Components (Default)**
- **<PERSON><PERSON><PERSON> đích**: Render trên server, tối ưu SEO và performance
- **Đặc điểm**: Không có state, event handlers, hoặc browser APIs
- **Use cases**: Layout, static content, data fetching

### **Client Components ('use client')**
- **Mục đích**: Interactive components với state và event handlers
- **Đặc điểm**: Có useState, useEffect, onClick handlers
- **Use cases**: Forms, buttons, interactive UI

## 🏗️ Zenera Component Classification

### **Server Components (No 'use client')**
```typescript
// ✅ Server Component - Static, no interactivity
export function Card({ children, className }) {
  return (
    <div className={cn('rounded-lg border', className)}>
      {children}
    </div>
  );
}
```

**Examples trong Zenera**:
- `Card`, `CardHeader`, `CardContent` - Layout components
- Static layout components
- Typography components

### **Client Components ('use client' required)**
```typescript
'use client';

// ✅ Client Component - Interactive với state/events
export function Button({ onClick, children }) {
  return (
    <button onClick={onClick}>
      {children}
    </button>
  );
}
```

**Examples trong Zenera**:
- `Button` - Event handlers (onClick)
- `Input` - Form interactions, focus events
- `ProductCard` - Click handlers, hover interactions
- `CartItem` - useState for quantity, async operations

## 📦 Zenera Component Architecture

### **Primitives Layer**
```
packages/ui-components/src/components/primitives/
├── Button.tsx          # 'use client' - onClick handlers
├── Input.tsx           # 'use client' - form interactions
├── Card.tsx            # Server Component - static layout
└── ...
```

### **Compositions Layer**
```
packages/ui-components/src/components/compositions/
├── ProductCard.tsx     # 'use client' - interactive product display
├── CartItem.tsx        # 'use client' - quantity state, actions
└── ...
```

### **Pages Layer**
```
packages/webapp/src/app/
├── page.tsx            # Server Component - static content
├── demo/page.tsx       # 'use client' - interactive demo
└── ...
```

## 🔧 Implementation Rules

### **Rule 1: Interactive = Client Component**
```typescript
// ❌ Server Component với event handler - LỖI
export function BadButton({ onClick }) {
  return <button onClick={onClick}>Click</button>; // Error!
}

// ✅ Client Component với event handler
'use client';
export function GoodButton({ onClick }) {
  return <button onClick={onClick}>Click</button>;
}
```

### **Rule 2: State = Client Component**
```typescript
// ❌ Server Component với useState - LỖI
export function BadCounter() {
  const [count, setCount] = useState(0); // Error!
  return <div>{count}</div>;
}

// ✅ Client Component với useState
'use client';
export function GoodCounter() {
  const [count, setCount] = useState(0);
  return <div>{count}</div>;
}
```

### **Rule 3: Event Handlers in Props = Client Component**
```typescript
// ❌ Server Component nhận event handlers - LỖI
export function BadCard({ onCardClick, children }) {
  return <div onClick={onCardClick}>{children}</div>; // Error!
}

// ✅ Client Component nhận event handlers
'use client';
export function GoodCard({ onCardClick, children }) {
  return <div onClick={onCardClick}>{children}</div>;
}
```

## 🎨 Zenera Specific Patterns

### **Pattern 1: Composition Strategy**
```typescript
// Server Component - Layout
export function ProductGrid({ children }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {children}
    </div>
  );
}

// Client Component - Interactive item
'use client';
export function ProductCard({ product, onAddToCart }) {
  return (
    <Card onClick={() => onAddToCart(product.id)}>
      {/* Interactive content */}
    </Card>
  );
}

// Usage in page
export default function ProductsPage() {
  return (
    <ProductGrid>
      <ProductCard product={product1} onAddToCart={handleAddToCart} />
      <ProductCard product={product2} onAddToCart={handleAddToCart} />
    </ProductGrid>
  );
}
```

### **Pattern 2: Demo Pages**
```typescript
// Demo pages cần 'use client' vì có interactive components
'use client';

export default function DemoPage() {
  const handleClick = () => console.log('clicked');
  
  return (
    <div>
      <Button onClick={handleClick}>Test Button</Button>
      <ProductCard onAddToCart={handleClick} />
    </div>
  );
}
```

### **Pattern 3: Form Components**
```typescript
// Form wrapper - Client Component
'use client';
export function ContactForm() {
  const [formData, setFormData] = useState({});
  
  return (
    <form>
      <Input 
        value={formData.email}
        onChange={(e) => setFormData({...formData, email: e.target.value})}
      />
      <Button type="submit">Submit</Button>
    </form>
  );
}
```

## 🚨 Common Errors & Solutions

### **Error 1: Event handlers cannot be passed to Client Component props**
```typescript
// ❌ Problem: Server component passing handlers to client component
export default function ServerPage() {
  const handleClick = () => {}; // Server function
  return <Button onClick={handleClick} />; // Error!
}

// ✅ Solution: Make page a client component
'use client';
export default function ClientPage() {
  const handleClick = () => {};
  return <Button onClick={handleClick} />;
}
```

### **Error 2: useState only works in Client Components**
```typescript
// ❌ Problem: useState in server component
export function BadComponent() {
  const [state, setState] = useState(0); // Error!
  return <div>{state}</div>;
}

// ✅ Solution: Add 'use client'
'use client';
export function GoodComponent() {
  const [state, setState] = useState(0);
  return <div>{state}</div>;
}
```

## 📊 Performance Considerations

### **Server Components Benefits**
- ✅ Faster initial page load
- ✅ Better SEO
- ✅ Smaller client bundle
- ✅ Direct database access

### **Client Components Benefits**
- ✅ Interactive functionality
- ✅ Real-time updates
- ✅ Rich user experience
- ✅ Browser APIs access

### **Best Practices**
1. **Default to Server Components** - Only use 'use client' when needed
2. **Minimize Client Boundaries** - Keep interactive parts small
3. **Compose Strategically** - Mix server and client components effectively
4. **Test Thoroughly** - Verify both server and client rendering

## 🔄 Migration Strategy

### **From Class Components**
```typescript
// Old: Class component (always client)
class OldButton extends Component {
  render() {
    return <button onClick={this.props.onClick}>Click</button>;
  }
}

// New: Function component với 'use client'
'use client';
export function NewButton({ onClick }) {
  return <button onClick={onClick}>Click</button>;
}
```

### **From Pages Router**
```typescript
// Old: pages/demo.tsx (always client)
export default function Demo() {
  const [state, setState] = useState();
  return <div>{state}</div>;
}

// New: app/demo/page.tsx (explicit client)
'use client';
export default function Demo() {
  const [state, setState] = useState();
  return <div>{state}</div>;
}
```

## ✅ Zenera Implementation Checklist

- [x] All interactive components have 'use client'
- [x] Static layout components are server components
- [x] Demo pages are client components
- [x] Form components are client components
- [x] Event handlers work correctly
- [x] State management works correctly
- [x] No runtime errors in development
- [x] Performance optimized (minimal client components)

## 🎯 Next Steps

1. **Audit existing components** - Ensure correct client/server classification
2. **Add type safety** - Create types for component props
3. **Performance monitoring** - Track bundle sizes and render times
4. **Documentation** - Document component classification decisions
