/**
 * Currency Utils Tests
 */

import { CurrencyUtils, PriceUtils, ZeneraCurrency } from '../currency-utils';

describe('CurrencyUtils', () => {
  describe('format', () => {
    it('should format USD currency correctly', () => {
      expect(CurrencyUtils.format(1234.56, ZeneraCurrency.USD)).toBe('$1,234.56');
    });

    it('should format VND currency correctly', () => {
      expect(CurrencyUtils.format(1234567, ZeneraCurrency.VND)).toBe('1.234.567 ₫');
    });

    it('should handle zero values', () => {
      expect(CurrencyUtils.format(0, ZeneraCurrency.USD)).toBe('$0.00');
    });

    it('should handle string input', () => {
      expect(CurrencyUtils.format('1234.56', ZeneraCurrency.USD)).toBe('$1,234.56');
    });

    it('should handle invalid input', () => {
      expect(CurrencyUtils.format('invalid', ZeneraCurrency.USD)).toBe('0');
    });
  });

  describe('formatWithSymbol', () => {
    it('should format with symbol at start', () => {
      expect(CurrencyUtils.formatWithSymbol(1234.56, ZeneraCurrency.USD, 'start')).toBe('$1,234.56');
    });

    it('should format with symbol at end', () => {
      expect(CurrencyUtils.formatWithSymbol(1234.56, ZeneraCurrency.USD, 'end')).toBe('1,234.56 $');
    });

    it('should handle VND special formatting', () => {
      expect(CurrencyUtils.formatWithSymbol(1234567, ZeneraCurrency.VND, 'end')).toBe('1.234.567đ');
    });
  });

  describe('convert', () => {
    it('should convert between currencies', () => {
      const rates = {
        [ZeneraCurrency.USD]: 1,
        [ZeneraCurrency.VND]: 24000,
      };
      
      const result = CurrencyUtils.convert(100, ZeneraCurrency.USD, ZeneraCurrency.VND, rates);
      expect(result).toBe(2400000);
    });

    it('should return same amount for same currency', () => {
      const result = CurrencyUtils.convert(100, ZeneraCurrency.USD, ZeneraCurrency.USD);
      expect(result).toBe(100);
    });
  });

  describe('round', () => {
    it('should round to currency precision', () => {
      expect(CurrencyUtils.round(1234.567, ZeneraCurrency.USD)).toBe(1234.57);
      expect(CurrencyUtils.round(1234.567, ZeneraCurrency.VND)).toBe(1235);
    });
  });

  describe('validation', () => {
    it('should validate currency codes', () => {
      expect(CurrencyUtils.isValidCurrency('USD')).toBe(true);
      expect(CurrencyUtils.isValidCurrency('INVALID')).toBe(false);
    });
  });

  describe('parse', () => {
    it('should parse currency strings', () => {
      expect(CurrencyUtils.parse('$1,234.56')).toBe(1234.56);
      expect(CurrencyUtils.parse('€1.234,56')).toBe(1234.56);
      expect(CurrencyUtils.parse('1,234.56đ')).toBe(1234.56);
    });
  });
});

describe('PriceUtils', () => {
  describe('calculateDiscountPercentage', () => {
    it('should calculate discount percentage correctly', () => {
      expect(PriceUtils.calculateDiscountPercentage(100, 80)).toBe(20);
      expect(PriceUtils.calculateDiscountPercentage(100, 50)).toBe(50);
    });

    it('should handle zero original price', () => {
      expect(PriceUtils.calculateDiscountPercentage(0, 50)).toBe(0);
    });
  });

  describe('calculateSalePrice', () => {
    it('should calculate sale price correctly', () => {
      expect(PriceUtils.calculateSalePrice(100, 20)).toBe(80);
      expect(PriceUtils.calculateSalePrice(100, 50)).toBe(50);
    });
  });

  describe('formatPriceRange', () => {
    it('should format price range', () => {
      expect(PriceUtils.formatPriceRange(10, 20, ZeneraCurrency.USD)).toBe('$10.00 - $20.00');
    });

    it('should format single price when min equals max', () => {
      expect(PriceUtils.formatPriceRange(10, 10, ZeneraCurrency.USD)).toBe('$10.00');
    });
  });

  describe('isFree', () => {
    it('should detect free prices', () => {
      expect(PriceUtils.isFree(0)).toBe(true);
      expect(PriceUtils.isFree(10)).toBe(false);
    });
  });

  describe('formatPriceOrFree', () => {
    it('should format free prices', () => {
      expect(PriceUtils.formatPriceOrFree(0)).toBe('Free');
    });

    it('should format regular prices', () => {
      expect(PriceUtils.formatPriceOrFree(10, ZeneraCurrency.USD)).toBe('$10.00');
    });
  });
});
