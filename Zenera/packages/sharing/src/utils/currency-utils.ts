/**
 * Currency Utilities
 * Extracted và adapted từ Medoo cho Zenera E-commerce
 */

// Currency Types
export enum ZeneraCurrency {
  USD = "USD",
  EUR = "EUR",
  VND = "VND",
  JPY = "JPY",
  GBP = "GBP",
  CAD = "CAD",
  AUD = "AUD",
  CNY = "CNY",
  THB = "THB",
  SGD = "SGD",
}

export const CurrencySymbol = {
  [ZeneraCurrency.USD]: "$",
  [ZeneraCurrency.EUR]: "€",
  [ZeneraCurrency.VND]: "đ",
  [ZeneraCurrency.JPY]: "¥",
  [ZeneraCurrency.GBP]: "£",
  [ZeneraCurrency.CAD]: "C$",
  [ZeneraCurrency.AUD]: "A$",
  [ZeneraCurrency.CNY]: "¥",
  [ZeneraCurrency.THB]: "฿",
  [ZeneraCurrency.SGD]: "S$",
} as const;

export const CurrencyLabel = {
  [ZeneraCurrency.USD]: "US Dollar",
  [ZeneraCurrency.EUR]: "Euro",
  [ZeneraCurrency.VND]: "Vietnamese Dong",
  [ZeneraCurrency.JPY]: "Japanese Yen",
  [ZeneraCurrency.GBP]: "British Pound",
  [ZeneraCurrency.CAD]: "Canadian Dollar",
  [ZeneraCurrency.AUD]: "Australian Dollar",
  [ZeneraCurrency.CNY]: "Chinese Yuan",
  [ZeneraCurrency.THB]: "Thai Baht",
  [ZeneraCurrency.SGD]: "Singapore Dollar",
} as const;

export const CurrencyDecimalPlaces = {
  [ZeneraCurrency.USD]: 2,
  [ZeneraCurrency.EUR]: 2,
  [ZeneraCurrency.VND]: 0,
  [ZeneraCurrency.JPY]: 0,
  [ZeneraCurrency.GBP]: 2,
  [ZeneraCurrency.CAD]: 2,
  [ZeneraCurrency.AUD]: 2,
  [ZeneraCurrency.CNY]: 2,
  [ZeneraCurrency.THB]: 2,
  [ZeneraCurrency.SGD]: 2,
} as const;

export const CurrencyLocale = {
  [ZeneraCurrency.USD]: "en-US",
  [ZeneraCurrency.EUR]: "de-DE",
  [ZeneraCurrency.VND]: "vi-VN",
  [ZeneraCurrency.JPY]: "ja-JP",
  [ZeneraCurrency.GBP]: "en-GB",
  [ZeneraCurrency.CAD]: "en-CA",
  [ZeneraCurrency.AUD]: "en-AU",
  [ZeneraCurrency.CNY]: "zh-CN",
  [ZeneraCurrency.THB]: "th-TH",
  [ZeneraCurrency.SGD]: "en-SG",
} as const;

/**
 * Currency Utilities
 * Adapted từ Medoo FormatterUtils và PriceUtils
 */
export const CurrencyUtils = {
  /**
   * Format currency với Intl.NumberFormat
   * Adapted từ Medoo FormatUtils.formatCurrency
   */
  format(
    amount: number | string,
    currency: ZeneraCurrency = ZeneraCurrency.USD,
    options?: Intl.NumberFormatOptions
  ): string {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numericAmount)) {
      return '0';
    }

    const locale = CurrencyLocale[currency] || 'en-US';
    const decimalPlaces = CurrencyDecimalPlaces[currency] || 2;

    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
      ...options,
    }).format(numericAmount);
  },

  /**
   * Format price với symbol position control
   * Adapted từ Medoo PriceUtils.mapPriceWithSymbol
   */
  formatWithSymbol(
    amount: number | string,
    currency: ZeneraCurrency = ZeneraCurrency.USD,
    symbolPosition: 'start' | 'end' = 'start'
  ): string {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numericAmount)) {
      return '0';
    }

    const formattedNumber = CurrencyUtils.formatNumber(numericAmount, currency);
    const symbol = CurrencySymbol[currency] || '';

    if (symbolPosition === 'start') {
      return `${symbol}${formattedNumber}`;
    } else {
      // Special handling cho VND như Medoo
      if (currency === ZeneraCurrency.VND) {
        return `${formattedNumber}${symbol}`;
      }
      return `${formattedNumber} ${symbol}`;
    }
  },

  /**
   * Format number without currency symbol
   * Adapted từ Medoo NumberSharingUtils.format
   */
  formatNumber(
    amount: number | string,
    currency: ZeneraCurrency = ZeneraCurrency.USD
  ): string {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numericAmount)) {
      return '0';
    }

    const locale = CurrencyLocale[currency] || 'en-US';
    const decimalPlaces = CurrencyDecimalPlaces[currency] || 2;

    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    }).format(numericAmount);
  },

  /**
   * Format với thousand separators
   * Adapted từ Medoo NumberSharingUtils.formatNumberWithThousandSeparators
   */
  formatWithSeparators(
    amount: number | string,
    separator: string = ',',
    decimalSeparator: string = '.'
  ): string {
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numericAmount)) {
      return '0';
    }

    const parts = numericAmount.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    
    return parts.join(decimalSeparator);
  },

  /**
   * Convert currency (basic implementation)
   * Adapted từ Medoo convertCurrency
   */
  convert(
    amount: number,
    fromCurrency: ZeneraCurrency,
    toCurrency: ZeneraCurrency,
    exchangeRates: Record<string, number> = {}
  ): number {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    const fromRate = exchangeRates[fromCurrency] || 1;
    const toRate = exchangeRates[toCurrency] || 1;

    // Convert to base currency (USD) then to target currency
    const baseAmount = amount / fromRate;
    return baseAmount * toRate;
  },

  /**
   * Get currency symbol
   */
  getSymbol(currency: ZeneraCurrency): string {
    return CurrencySymbol[currency] || '';
  },

  /**
   * Get currency label
   */
  getLabel(currency: ZeneraCurrency): string {
    return CurrencyLabel[currency] || currency;
  },

  /**
   * Get decimal places for currency
   */
  getDecimalPlaces(currency: ZeneraCurrency): number {
    return CurrencyDecimalPlaces[currency] || 2;
  },

  /**
   * Validate currency code
   */
  isValidCurrency(currency: string): currency is ZeneraCurrency {
    return Object.values(ZeneraCurrency).includes(currency as ZeneraCurrency);
  },

  /**
   * Parse currency string to number
   */
  parse(currencyString: string): number {
    // Remove currency symbols and separators
    const cleanString = currencyString
      .replace(/[^\d.-]/g, '')
      .replace(/,/g, '');
    
    return parseFloat(cleanString) || 0;
  },

  /**
   * Round to currency precision
   * Adapted từ Medoo NumberSharingUtils.toFixed
   */
  round(
    amount: number,
    currency: ZeneraCurrency = ZeneraCurrency.USD
  ): number {
    const decimalPlaces = CurrencyDecimalPlaces[currency] || 2;
    return Math.round(amount * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
  },
};

/**
 * E-commerce Price Utilities
 * Adapted từ Medoo EcommerceUtils
 */
export const PriceUtils = {
  /**
   * Format price cho e-commerce display
   */
  formatPrice(
    price: number | string,
    currency: ZeneraCurrency = ZeneraCurrency.USD
  ): string {
    return CurrencyUtils.format(price, currency);
  },

  /**
   * Calculate discount percentage
   * Adapted từ Medoo EcommerceUtils.calculateDiscount
   */
  calculateDiscountPercentage(
    originalPrice: number,
    salePrice: number
  ): number {
    if (originalPrice <= 0) return 0;
    return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
  },

  /**
   * Calculate discount amount
   */
  calculateDiscountAmount(
    originalPrice: number,
    discountPercentage: number
  ): number {
    return originalPrice * (discountPercentage / 100);
  },

  /**
   * Calculate sale price from discount
   */
  calculateSalePrice(
    originalPrice: number,
    discountPercentage: number
  ): number {
    const discountAmount = PriceUtils.calculateDiscountAmount(originalPrice, discountPercentage);
    return originalPrice - discountAmount;
  },

  /**
   * Format price range
   */
  formatPriceRange(
    minPrice: number,
    maxPrice: number,
    currency: ZeneraCurrency = ZeneraCurrency.USD
  ): string {
    if (minPrice === maxPrice) {
      return CurrencyUtils.format(minPrice, currency);
    }
    return `${CurrencyUtils.format(minPrice, currency)} - ${CurrencyUtils.format(maxPrice, currency)}`;
  },

  /**
   * Check if price is free
   */
  isFree(price: number): boolean {
    return price === 0;
  },

  /**
   * Format free price
   */
  formatFreePrice(): string {
    return 'Free';
  },

  /**
   * Format price with free check
   */
  formatPriceOrFree(
    price: number,
    currency: ZeneraCurrency = ZeneraCurrency.USD
  ): string {
    if (PriceUtils.isFree(price)) {
      return PriceUtils.formatFreePrice();
    }
    return PriceUtils.formatPrice(price, currency);
  },
};
