/**
 * String Manipulation Utilities
 * Extracted và adapted từ Medoo cho Zenera E-commerce
 */

/**
 * String Utilities
 * Adapted từ Medoo string manipulation utilities
 */
export const StringUtils = {
  /**
   * Create URL-friendly slug
   * Adapted từ existing slugify function
   */
  slugify(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  },

  /**
   * Capitalize first letter
   */
  capitalize(text: string): string {
    if (!text) return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  },

  /**
   * Capitalize each word
   */
  capitalizeWords(text: string): string {
    if (!text) return '';
    return text
      .split(' ')
      .map(word => StringUtils.capitalize(word))
      .join(' ');
  },

  /**
   * Convert to camelCase
   */
  toCamelCase(text: string): string {
    return text
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
        return index === 0 ? word.toLowerCase() : word.toUpperCase();
      })
      .replace(/\s+/g, '');
  },

  /**
   * Convert to PascalCase
   */
  toPascalCase(text: string): string {
    return text
      .replace(/(?:^\w|[A-Z]|\b\w)/g, word => word.toUpperCase())
      .replace(/\s+/g, '');
  },

  /**
   * Convert to kebab-case
   */
  toKebabCase(text: string): string {
    return text
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  },

  /**
   * Convert to snake_case
   */
  toSnakeCase(text: string): string {
    return text
      .replace(/([a-z])([A-Z])/g, '$1_$2')
      .replace(/[\s-]+/g, '_')
      .toLowerCase();
  },

  /**
   * Truncate text with ellipsis
   */
  truncate(text: string, maxLength: number, suffix: string = '...'): string {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
  },

  /**
   * Truncate text at word boundary
   */
  truncateWords(text: string, maxWords: number, suffix: string = '...'): string {
    if (!text) return '';
    
    const words = text.split(' ');
    if (words.length <= maxWords) return text;
    
    return words.slice(0, maxWords).join(' ') + suffix;
  },

  /**
   * Remove HTML tags
   */
  stripHtml(html: string): string {
    if (!html) return '';
    return html.replace(/<[^>]*>/g, '');
  },

  /**
   * Escape HTML characters
   */
  escapeHtml(text: string): string {
    if (!text) return '';
    
    const htmlEscapes: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;',
    };
    
    return text.replace(/[&<>"'/]/g, char => htmlEscapes[char]);
  },

  /**
   * Unescape HTML characters
   */
  unescapeHtml(text: string): string {
    if (!text) return '';
    
    const htmlUnescapes: Record<string, string> = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#x27;': "'",
      '&#x2F;': '/',
    };
    
    return text.replace(/&(?:amp|lt|gt|quot|#x27|#x2F);/g, entity => htmlUnescapes[entity]);
  },

  /**
   * Generate random string
   */
  generateRandom(length: number = 8, charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
  },

  /**
   * Generate UUID-like string
   */
  generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  },

  /**
   * Check if string is empty or whitespace
   */
  isEmpty(text: string | null | undefined): boolean {
    return !text || text.trim().length === 0;
  },

  /**
   * Check if string is not empty
   */
  isNotEmpty(text: string | null | undefined): boolean {
    return !StringUtils.isEmpty(text);
  },

  /**
   * Pad string with characters
   */
  pad(text: string, length: number, padChar: string = ' ', padLeft: boolean = true): string {
    if (text.length >= length) return text;
    
    const padLength = length - text.length;
    const padding = padChar.repeat(padLength);
    
    return padLeft ? padding + text : text + padding;
  },

  /**
   * Remove extra whitespace
   */
  normalizeWhitespace(text: string): string {
    if (!text) return '';
    return text.replace(/\s+/g, ' ').trim();
  },

  /**
   * Count words
   */
  wordCount(text: string): number {
    if (!text) return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  },

  /**
   * Extract numbers from string
   */
  extractNumbers(text: string): number[] {
    if (!text) return [];
    const matches = text.match(/\d+/g);
    return matches ? matches.map(Number) : [];
  },

  /**
   * Check if string contains only numbers
   */
  isNumeric(text: string): boolean {
    return /^\d+$/.test(text);
  },

  /**
   * Check if string is valid email
   */
  isEmail(text: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(text);
  },

  /**
   * Check if string is valid URL
   */
  isUrl(text: string): boolean {
    try {
      new URL(text);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Mask sensitive information
   */
  mask(text: string, visibleChars: number = 4, maskChar: string = '*'): string {
    if (!text || text.length <= visibleChars) return text;
    
    const visible = text.slice(-visibleChars);
    const masked = maskChar.repeat(text.length - visibleChars);
    
    return masked + visible;
  },

  /**
   * Format phone number
   */
  formatPhone(phone: string, format: string = '(###) ###-####'): string {
    if (!phone) return '';
    
    const cleaned = phone.replace(/\D/g, '');
    let formatted = format;
    
    for (let i = 0; i < cleaned.length; i++) {
      formatted = formatted.replace('#', cleaned[i]);
    }
    
    return formatted.replace(/#/g, '');
  },
};

/**
 * E-commerce specific string utilities
 */
export const EcommerceStringUtils = {
  /**
   * Generate SKU from product name
   * Adapted từ Medoo EcommerceUtils.generateSKU
   */
  generateSKU(productName: string, prefix: string = ''): string {
    const cleanName = productName
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 8);
    
    const timestamp = Date.now().toString().slice(-4);
    const random = StringUtils.generateRandom(2, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
    
    return `${prefix}${cleanName}-${timestamp}${random}`;
  },

  /**
   * Format product title for SEO
   */
  formatProductTitle(title: string, maxLength: number = 60): string {
    if (!title) return '';
    
    // Clean and normalize
    const cleaned = StringUtils.normalizeWhitespace(title);
    
    // Truncate if needed
    return StringUtils.truncate(cleaned, maxLength);
  },

  /**
   * Generate meta description
   */
  generateMetaDescription(description: string, maxLength: number = 160): string {
    if (!description) return '';
    
    // Strip HTML and normalize
    const cleaned = StringUtils.normalizeWhitespace(StringUtils.stripHtml(description));
    
    // Truncate at word boundary
    return StringUtils.truncateWords(cleaned, 25, '...');
  },

  /**
   * Format category path
   */
  formatCategoryPath(categories: string[]): string {
    return categories
      .filter(cat => StringUtils.isNotEmpty(cat))
      .map(cat => StringUtils.capitalizeWords(cat.trim()))
      .join(' > ');
  },

  /**
   * Generate product URL slug
   */
  generateProductSlug(title: string, sku?: string): string {
    const titleSlug = StringUtils.slugify(title);
    
    if (sku) {
      const skuSlug = StringUtils.slugify(sku);
      return `${titleSlug}-${skuSlug}`;
    }
    
    return titleSlug;
  },

  /**
   * Format review excerpt
   */
  formatReviewExcerpt(review: string, maxLength: number = 100): string {
    if (!review) return '';
    
    const cleaned = StringUtils.normalizeWhitespace(StringUtils.stripHtml(review));
    return StringUtils.truncate(cleaned, maxLength);
  },

  /**
   * Validate and format product code
   */
  formatProductCode(code: string): string {
    if (!code) return '';
    
    return code
      .toUpperCase()
      .replace(/[^A-Z0-9-]/g, '')
      .trim();
  },

  /**
   * Generate search keywords from product data
   */
  generateSearchKeywords(title: string, description: string, category: string): string[] {
    const allText = `${title} ${description} ${category}`.toLowerCase();
    
    // Extract meaningful words (3+ characters)
    const words = allText
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length >= 3)
      .filter((word, index, arr) => arr.indexOf(word) === index); // Remove duplicates
    
    return words.slice(0, 20); // Limit to 20 keywords
  },

  /**
   * Format shipping address
   */
  formatShippingAddress(address: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  }): string {
    const parts = [
      address.street,
      address.city,
      address.state,
      address.zipCode,
      address.country,
    ].filter(part => StringUtils.isNotEmpty(part));
    
    return parts.join(', ');
  },

  /**
   * Validate and format discount code
   */
  formatDiscountCode(code: string): string {
    if (!code) return '';
    
    return code
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .trim();
  },
};
