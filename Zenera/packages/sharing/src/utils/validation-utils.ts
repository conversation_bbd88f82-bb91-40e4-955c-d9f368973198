/**
 * Validation Utilities
 * Extracted và adapted từ Medoo cho Zenera E-commerce
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface ValidationRule<T = any> {
  validate: (value: T) => boolean;
  message: string;
}

/**
 * Basic Validation Utilities
 * Adapted từ Medoo validation utilities
 */
export const ValidationUtils = {
  /**
   * Validate email address
   */
  isValidEmail(email: string): boolean {
    if (!email) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  },

  /**
   * Validate password strength
   */
  isValidPassword(password: string, minLength: number = 8): ValidationResult {
    const errors: string[] = [];

    if (!password) {
      errors.push('Password is required');
      return { isValid: false, errors };
    }

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate phone number
   */
  isValidPhone(phone: string): boolean {
    if (!phone) return false;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleaned = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleaned);
  },

  /**
   * Validate URL
   */
  isValidUrl(url: string): boolean {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Validate credit card number (Luhn algorithm)
   */
  isValidCreditCard(cardNumber: string): boolean {
    if (!cardNumber) return false;
    
    const cleaned = cardNumber.replace(/\s/g, '');
    if (!/^\d+$/.test(cleaned)) return false;

    let sum = 0;
    let isEven = false;

    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  },

  /**
   * Validate date
   */
  isValidDate(date: string | Date): boolean {
    if (!date) return false;
    const dateObj = new Date(date);
    return !isNaN(dateObj.getTime());
  },

  /**
   * Validate age (18+)
   */
  isValidAge(birthDate: string | Date, minAge: number = 18): boolean {
    if (!ValidationUtils.isValidDate(birthDate)) return false;
    
    const birth = new Date(birthDate);
    const today = new Date();
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1 >= minAge;
    }
    
    return age >= minAge;
  },

  /**
   * Validate required field
   */
  isRequired(value: any): boolean {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0;
    return true;
  },

  /**
   * Validate string length
   */
  isValidLength(value: string, min: number = 0, max: number = Infinity): boolean {
    if (!value) return min === 0;
    return value.length >= min && value.length <= max;
  },

  /**
   * Validate number range
   */
  isInRange(value: number, min: number = -Infinity, max: number = Infinity): boolean {
    return value >= min && value <= max;
  },

  /**
   * Validate array length
   */
  isValidArrayLength(array: any[], min: number = 0, max: number = Infinity): boolean {
    return array.length >= min && array.length <= max;
  },

  /**
   * Validate file size
   */
  isValidFileSize(file: File, maxSizeInMB: number): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  },

  /**
   * Validate file type
   */
  isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type);
  },

  /**
   * Validate image dimensions (placeholder for browser implementation)
   * Note: This function should be implemented in browser-specific code
   */
  async isValidImageDimensions(
    file: File,
    maxWidth: number,
    maxHeight: number
  ): Promise<boolean> {
    // This is a placeholder implementation
    // In actual browser usage, this should be implemented with proper DOM APIs
    // For now, we'll just validate file size as a proxy
    const maxSizeForDimensions = 5 * 1024 * 1024; // 5MB
    return file.size <= maxSizeForDimensions;
  },

  /**
   * Generic validation with rules
   */
  validate<T>(value: T, rules: ValidationRule<T>[]): ValidationResult {
    const errors: string[] = [];

    for (const rule of rules) {
      if (!rule.validate(value)) {
        errors.push(rule.message);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};

/**
 * E-commerce specific validation utilities
 */
export const EcommerceValidationUtils = {
  /**
   * Validate product price
   */
  isValidPrice(price: number): ValidationResult {
    const errors: string[] = [];

    if (price === null || price === undefined) {
      errors.push('Price is required');
    } else if (price < 0) {
      errors.push('Price cannot be negative');
    } else if (price > 1000000) {
      errors.push('Price cannot exceed $1,000,000');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate product SKU
   */
  isValidSKU(sku: string): ValidationResult {
    const errors: string[] = [];

    if (!sku) {
      errors.push('SKU is required');
    } else if (sku.length < 3) {
      errors.push('SKU must be at least 3 characters long');
    } else if (sku.length > 50) {
      errors.push('SKU cannot exceed 50 characters');
    } else if (!/^[A-Z0-9\-_]+$/i.test(sku)) {
      errors.push('SKU can only contain letters, numbers, hyphens, and underscores');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate product inventory
   */
  isValidInventory(quantity: number): ValidationResult {
    const errors: string[] = [];

    if (quantity === null || quantity === undefined) {
      errors.push('Inventory quantity is required');
    } else if (quantity < 0) {
      errors.push('Inventory quantity cannot be negative');
    } else if (!Number.isInteger(quantity)) {
      errors.push('Inventory quantity must be a whole number');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate discount percentage
   */
  isValidDiscountPercentage(discount: number): ValidationResult {
    const errors: string[] = [];

    if (discount === null || discount === undefined) {
      errors.push('Discount percentage is required');
    } else if (discount < 0) {
      errors.push('Discount percentage cannot be negative');
    } else if (discount > 100) {
      errors.push('Discount percentage cannot exceed 100%');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate shipping address
   */
  isValidShippingAddress(address: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  }): ValidationResult {
    const errors: string[] = [];

    if (!address.street || address.street.trim().length === 0) {
      errors.push('Street address is required');
    }

    if (!address.city || address.city.trim().length === 0) {
      errors.push('City is required');
    }

    if (!address.state || address.state.trim().length === 0) {
      errors.push('State/Province is required');
    }

    if (!address.zipCode || address.zipCode.trim().length === 0) {
      errors.push('ZIP/Postal code is required');
    }

    if (!address.country || address.country.trim().length === 0) {
      errors.push('Country is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate order quantity
   */
  isValidOrderQuantity(quantity: number, maxQuantity?: number): ValidationResult {
    const errors: string[] = [];

    if (quantity === null || quantity === undefined) {
      errors.push('Quantity is required');
    } else if (quantity <= 0) {
      errors.push('Quantity must be greater than 0');
    } else if (!Number.isInteger(quantity)) {
      errors.push('Quantity must be a whole number');
    } else if (maxQuantity && quantity > maxQuantity) {
      errors.push(`Quantity cannot exceed ${maxQuantity}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate product images
   */
  async isValidProductImages(files: File[]): Promise<ValidationResult> {
    const errors: string[] = [];

    if (files.length === 0) {
      errors.push('At least one product image is required');
      return { isValid: false, errors };
    }

    if (files.length > 10) {
      errors.push('Cannot upload more than 10 images');
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const maxSizeInMB = 5;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (!ValidationUtils.isValidFileType(file, allowedTypes)) {
        errors.push(`Image ${i + 1}: Only JPEG, PNG, and WebP formats are allowed`);
      }

      if (!ValidationUtils.isValidFileSize(file, maxSizeInMB)) {
        errors.push(`Image ${i + 1}: File size cannot exceed ${maxSizeInMB}MB`);
      }

      // Check image dimensions
      try {
        const isValidDimensions = await ValidationUtils.isValidImageDimensions(file, 2000, 2000);
        if (!isValidDimensions) {
          errors.push(`Image ${i + 1}: Dimensions cannot exceed 2000x2000 pixels`);
        }
      } catch {
        errors.push(`Image ${i + 1}: Invalid image file`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate coupon code
   */
  isValidCouponCode(code: string): ValidationResult {
    const errors: string[] = [];

    if (!code) {
      errors.push('Coupon code is required');
    } else if (code.length < 3) {
      errors.push('Coupon code must be at least 3 characters long');
    } else if (code.length > 20) {
      errors.push('Coupon code cannot exceed 20 characters');
    } else if (!/^[A-Z0-9]+$/i.test(code)) {
      errors.push('Coupon code can only contain letters and numbers');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate seller information
   */
  isValidSellerInfo(seller: {
    storeName?: string;
    businessEmail?: string;
    businessPhone?: string;
    businessAddress?: string;
  }): ValidationResult {
    const errors: string[] = [];

    if (!seller.storeName || seller.storeName.trim().length === 0) {
      errors.push('Store name is required');
    } else if (seller.storeName.length > 100) {
      errors.push('Store name cannot exceed 100 characters');
    }

    if (!seller.businessEmail) {
      errors.push('Business email is required');
    } else if (!ValidationUtils.isValidEmail(seller.businessEmail)) {
      errors.push('Business email is not valid');
    }

    if (seller.businessPhone && !ValidationUtils.isValidPhone(seller.businessPhone)) {
      errors.push('Business phone number is not valid');
    }

    if (!seller.businessAddress || seller.businessAddress.trim().length === 0) {
      errors.push('Business address is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};
