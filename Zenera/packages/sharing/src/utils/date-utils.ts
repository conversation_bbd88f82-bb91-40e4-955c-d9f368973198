/**
 * Date/Time Utilities
 * Extracted và adapted từ Medoo cho Zenera E-commerce
 */

export type DateInput = Date | string | number;

export interface DateFormatOptions {
  locale?: string;
  timeZone?: string;
  includeTime?: boolean;
  format?: 'short' | 'medium' | 'long' | 'full';
}

/**
 * Date Utilities
 * Adapted từ Medoo date formatting utilities
 */
export const DateUtils = {
  /**
   * Format date với Intl.DateTimeFormat
   * Adapted từ Medoo date formatting
   */
  format(
    date: DateInput,
    options: DateFormatOptions = {}
  ): string {
    const {
      locale = 'en-US',
      timeZone = 'UTC',
      includeTime = false,
      format = 'medium'
    } = options;

    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return '';

    const formatOptions: Intl.DateTimeFormatOptions = {
      timeZone,
    };

    // Date formatting
    switch (format) {
      case 'short':
        formatOptions.dateStyle = 'short';
        break;
      case 'medium':
        formatOptions.dateStyle = 'medium';
        break;
      case 'long':
        formatOptions.dateStyle = 'long';
        break;
      case 'full':
        formatOptions.dateStyle = 'full';
        break;
      default:
        formatOptions.year = 'numeric';
        formatOptions.month = 'long';
        formatOptions.day = 'numeric';
    }

    // Time formatting
    if (includeTime) {
      formatOptions.timeStyle = 'short';
    }

    return new Intl.DateTimeFormat(locale, formatOptions).format(dateObj);
  },

  /**
   * Format date cho Vietnamese locale
   */
  formatVN(
    date: DateInput,
    includeTime: boolean = false
  ): string {
    return DateUtils.format(date, {
      locale: 'vi-VN',
      timeZone: 'Asia/Ho_Chi_Minh',
      includeTime,
      format: 'long'
    });
  },

  /**
   * Format relative time (e.g., "2 hours ago")
   * Adapted từ Medoo relative time formatting
   */
  formatRelative(
    date: DateInput,
    locale: string = 'en-US'
  ): string {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return '';

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    // Use Intl.RelativeTimeFormat for modern browsers
    if (typeof Intl !== 'undefined' && Intl.RelativeTimeFormat) {
      const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

      if (diffInSeconds < 60) {
        return rtf.format(-diffInSeconds, 'second');
      } else if (diffInSeconds < 3600) {
        return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
      } else if (diffInSeconds < 86400) {
        return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
      } else if (diffInSeconds < 2592000) {
        return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
      } else if (diffInSeconds < 31536000) {
        return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
      } else {
        return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
      }
    }

    // Fallback for older browsers
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
      const years = Math.floor(diffInSeconds / 31536000);
      return `${years} year${years > 1 ? 's' : ''} ago`;
    }
  },

  /**
   * Convert input to Date object
   */
  toDate(input: DateInput): Date | null {
    if (input instanceof Date) {
      return isNaN(input.getTime()) ? null : input;
    }
    
    if (typeof input === 'string' || typeof input === 'number') {
      const date = new Date(input);
      return isNaN(date.getTime()) ? null : date;
    }
    
    return null;
  },

  /**
   * Check if date is valid
   */
  isValid(date: DateInput): boolean {
    return DateUtils.toDate(date) !== null;
  },

  /**
   * Get start of day
   */
  startOfDay(date: DateInput): Date | null {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return null;

    const result = new Date(dateObj);
    result.setHours(0, 0, 0, 0);
    return result;
  },

  /**
   * Get end of day
   */
  endOfDay(date: DateInput): Date | null {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return null;

    const result = new Date(dateObj);
    result.setHours(23, 59, 59, 999);
    return result;
  },

  /**
   * Add days to date
   */
  addDays(date: DateInput, days: number): Date | null {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return null;

    const result = new Date(dateObj);
    result.setDate(result.getDate() + days);
    return result;
  },

  /**
   * Add months to date
   */
  addMonths(date: DateInput, months: number): Date | null {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return null;

    const result = new Date(dateObj);
    result.setMonth(result.getMonth() + months);
    return result;
  },

  /**
   * Get difference in days
   */
  diffInDays(date1: DateInput, date2: DateInput): number {
    const d1 = DateUtils.toDate(date1);
    const d2 = DateUtils.toDate(date2);
    
    if (!d1 || !d2) return 0;

    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  /**
   * Check if date is today
   */
  isToday(date: DateInput): boolean {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return false;

    const today = new Date();
    return dateObj.toDateString() === today.toDateString();
  },

  /**
   * Check if date is yesterday
   */
  isYesterday(date: DateInput): boolean {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return false;

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return dateObj.toDateString() === yesterday.toDateString();
  },

  /**
   * Check if date is this week
   */
  isThisWeek(date: DateInput): boolean {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return false;

    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return dateObj >= startOfWeek && dateObj <= endOfWeek;
  },

  /**
   * Format for ISO string
   */
  toISOString(date: DateInput): string {
    const dateObj = DateUtils.toDate(date);
    return dateObj ? dateObj.toISOString() : '';
  },

  /**
   * Format for API (YYYY-MM-DD)
   */
  toAPIFormat(date: DateInput): string {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return '';

    return dateObj.toISOString().split('T')[0];
  },

  /**
   * Parse API format (YYYY-MM-DD)
   */
  fromAPIFormat(dateString: string): Date | null {
    if (!dateString) return null;
    return DateUtils.toDate(dateString);
  },
};

/**
 * Time Utilities
 */
export const TimeUtils = {
  /**
   * Format time only
   */
  formatTime(
    date: DateInput,
    locale: string = 'en-US',
    format: '12h' | '24h' = '12h'
  ): string {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return '';

    return new Intl.DateTimeFormat(locale, {
      hour: 'numeric',
      minute: '2-digit',
      hour12: format === '12h',
    }).format(dateObj);
  },

  /**
   * Get current timestamp
   */
  now(): number {
    return Date.now();
  },

  /**
   * Get current date
   */
  today(): Date {
    return new Date();
  },

  /**
   * Sleep utility
   */
  sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
};

/**
 * E-commerce specific date utilities
 */
export const EcommerceDateUtils = {
  /**
   * Format order date
   */
  formatOrderDate(date: DateInput): string {
    return DateUtils.format(date, {
      locale: 'en-US',
      includeTime: true,
      format: 'medium'
    });
  },

  /**
   * Format delivery date
   */
  formatDeliveryDate(date: DateInput): string {
    return DateUtils.format(date, {
      locale: 'en-US',
      format: 'long'
    });
  },

  /**
   * Calculate estimated delivery
   */
  calculateEstimatedDelivery(
    orderDate: DateInput,
    deliveryDays: number = 7
  ): Date | null {
    return DateUtils.addDays(orderDate, deliveryDays);
  },

  /**
   * Check if delivery is overdue
   */
  isDeliveryOverdue(estimatedDate: DateInput): boolean {
    const estimated = DateUtils.toDate(estimatedDate);
    if (!estimated) return false;

    return estimated < new Date();
  },

  /**
   * Format promotion expiry
   */
  formatPromotionExpiry(date: DateInput): string {
    const dateObj = DateUtils.toDate(date);
    if (!dateObj) return '';

    if (DateUtils.isToday(dateObj)) {
      return `Expires today at ${TimeUtils.formatTime(dateObj)}`;
    }

    if (DateUtils.isYesterday(dateObj)) {
      return 'Expired yesterday';
    }

    if (dateObj < new Date()) {
      return `Expired ${DateUtils.formatRelative(dateObj)}`;
    }

    return `Expires ${DateUtils.formatRelative(dateObj)}`;
  },
};
