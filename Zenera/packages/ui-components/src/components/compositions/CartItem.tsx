import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils';
import { Card, CardContent } from '../primitives/Card';
import { Button } from '../primitives/Button';
import { Input } from '../primitives/Input';

const cartItemVariants = cva(
  'group relative transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'hover:shadow-md',
        compact: 'hover:shadow-sm',
        detailed: 'hover:shadow-lg',
      },
      size: {
        sm: 'p-2',
        default: 'p-4',
        lg: 'p-6',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface CartItemProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cartItemVariants> {
  item: {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    image: string;
    quantity: number;
    maxQuantity?: number;
    variant?: string; // Size, Color, etc.
    inStock?: boolean;
  };
  onUpdateQuantity?: (itemId: string, quantity: number) => void;
  onRemove?: (itemId: string) => void;
  onMoveToWishlist?: (itemId: string) => void;
  loading?: boolean;
  showControls?: boolean;
}

const CartItem = React.forwardRef<HTMLDivElement, CartItemProps>(
  ({ 
    className, 
    variant, 
    size,
    item,
    onUpdateQuantity,
    onRemove,
    onMoveToWishlist,
    loading = false,
    showControls = true,
    ...props 
  }, ref) => {
    const [quantity, setQuantity] = React.useState(item.quantity);
    const [isUpdating, setIsUpdating] = React.useState(false);

    const handleQuantityChange = async (newQuantity: number) => {
      if (newQuantity < 1 || (item.maxQuantity && newQuantity > item.maxQuantity)) return;
      
      setIsUpdating(true);
      setQuantity(newQuantity);
      
      try {
        await onUpdateQuantity?.(item.id, newQuantity);
      } catch (error) {
        // Revert on error
        setQuantity(item.quantity);
      } finally {
        setIsUpdating(false);
      }
    };

    const totalPrice = item.price * quantity;
    const originalTotalPrice = item.originalPrice ? item.originalPrice * quantity : null;
    const discountPercentage = item.originalPrice 
      ? Math.round(((item.originalPrice - item.price) / item.originalPrice) * 100)
      : 0;

    return (
      <Card
        ref={ref}
        className={cn(cartItemVariants({ variant, size }), className)}
        padding="none"
        {...props}
      >
        <CardContent className="p-0">
          <div className="flex gap-4">
            {/* Product Image */}
            <div className="relative flex-shrink-0">
              <div className="h-20 w-20 overflow-hidden rounded-md bg-neutral-100">
                <img
                  src={item.image}
                  alt={item.name}
                  className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
                  loading="lazy"
                />
              </div>
              
              {/* Stock Status */}
              {item.inStock === false && (
                <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black/50">
                  <span className="text-xs font-medium text-white">Out of Stock</span>
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="flex flex-1 flex-col justify-between">
              <div>
                <h3 className="font-medium text-neutral-900 line-clamp-2">
                  {item.name}
                </h3>
                
                {item.variant && (
                  <p className="text-sm text-neutral-500 mt-1">
                    {item.variant}
                  </p>
                )}

                {/* Price */}
                <div className="flex items-center gap-2 mt-2">
                  <span className="font-semibold text-neutral-900">
                    ${totalPrice.toFixed(2)}
                  </span>
                  {originalTotalPrice && (
                    <>
                      <span className="text-sm text-neutral-500 line-through">
                        ${originalTotalPrice.toFixed(2)}
                      </span>
                      <span className="text-xs font-medium text-error-500">
                        -{discountPercentage}%
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Controls */}
              {showControls && (
                <div className="flex items-center justify-between mt-3">
                  {/* Quantity Controls */}
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-8 w-8 p-0"
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1 || isUpdating || loading}
                    >
                      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                      </svg>
                    </Button>
                    
                    <Input
                      type="number"
                      value={quantity}
                      onChange={(e) => {
                        const newQuantity = parseInt(e.target.value) || 1;
                        handleQuantityChange(newQuantity);
                      }}
                      className="h-8 w-16 text-center"
                      min={1}
                      max={item.maxQuantity}
                      disabled={isUpdating || loading}
                    />
                    
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-8 w-8 p-0"
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={
                        (item.maxQuantity && quantity >= item.maxQuantity) || 
                        isUpdating || 
                        loading
                      }
                    >
                      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </Button>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-1">
                    {onMoveToWishlist && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-2 text-xs"
                        onClick={() => onMoveToWishlist(item.id)}
                        disabled={loading}
                      >
                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        Save
                      </Button>
                    )}
                    
                    {onRemove && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-2 text-xs text-error-600 hover:text-error-700 hover:bg-error-50"
                        onClick={() => onRemove(item.id)}
                        disabled={loading}
                      >
                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

CartItem.displayName = 'CartItem';

export { CartItem, cartItemVariants };
