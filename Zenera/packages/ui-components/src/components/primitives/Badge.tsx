import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils';

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary-500 text-white hover:bg-primary-600',
        secondary:
          'border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200',
        destructive:
          'border-transparent bg-error-500 text-white hover:bg-error-600',
        success:
          'border-transparent bg-success-500 text-white hover:bg-success-600',
        warning:
          'border-transparent bg-warning-500 text-white hover:bg-warning-600',
        outline: 
          'border-neutral-200 text-neutral-900 hover:bg-neutral-50',
        // E-commerce specific variants
        sale:
          'border-transparent bg-gradient-to-r from-error-500 to-error-600 text-white animate-pulse',
        new:
          'border-transparent bg-gradient-to-r from-success-500 to-success-600 text-white',
        featured:
          'border-transparent bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:shadow-lg hover:shadow-primary-500/25',
        bestseller:
          'border-transparent bg-gradient-to-r from-warning-500 to-warning-600 text-white',
        outOfStock:
          'border-transparent bg-neutral-500 text-white',
        inStock:
          'border-transparent bg-success-500 text-white',
        lowStock:
          'border-transparent bg-warning-500 text-white animate-pulse',
      },
      size: {
        default: 'px-2.5 py-0.5 text-xs',
        sm: 'px-2 py-0.5 text-xs',
        lg: 'px-3 py-1 text-sm',
        xl: 'px-4 py-1.5 text-base',
      },
      animation: {
        none: '',
        pulse: 'animate-pulse',
        bounce: 'animate-bounce',
        glow: 'hover:shadow-lg transition-shadow duration-200',
        scale: 'hover:scale-105 transition-transform duration-200',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      animation: 'none',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  icon?: React.ReactNode;
  closable?: boolean;
  onClose?: () => void;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ 
    className, 
    variant, 
    size, 
    animation,
    icon,
    closable = false,
    onClose,
    children,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, size, animation }), className)}
        {...props}
      >
        {icon && (
          <span className="mr-1 flex-shrink-0">
            {icon}
          </span>
        )}
        {children}
        {closable && onClose && (
          <button
            type="button"
            className="ml-1 flex-shrink-0 rounded-full p-0.5 hover:bg-black/10 focus:outline-none focus:ring-1 focus:ring-white"
            onClick={onClose}
          >
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>
    );
  }
);

Badge.displayName = 'Badge';

export { Badge, badgeVariants };
