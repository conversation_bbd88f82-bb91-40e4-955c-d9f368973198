import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmail(email);
    if (user && await bcrypt.compare(password, user.password)) {
      const { password, ...result } = user.toObject();
      return result;
    }
    return null;
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = { email: user.email, sub: user._id, role: user.role };
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const { email, password, username, role, first_name, last_name, phone_number } = registerDto;

    // Check if user exists
    const existingUser = await this.usersService.findByEmailOrUsername(email, username);
    if (existingUser) {
      throw new UnauthorizedException('Email or username already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Initialize user data based on role
    const userData: any = {
      email,
      password: hashedPassword,
      username,
      first_name,
      last_name,
      phone_number,
      roles: [role || 'customer'],
    };

    // Add role-specific information
    switch (role) {
      case 'customer':
        userData.customer_info = {
          points: 0,
          addresses: [],
        };
        break;
      case 'seller':
        userData.seller_info = {
          store_name: `${username || first_name}'s store`,
          is_approved: false,
          store_addresses: [],
        };
        break;
      case 'admin':
        userData.admin_info = {
          admin_role: 'admin',
          permissions: ['read', 'write'],
          assigned_areas: [],
        };
        break;
      case 'super_admin':
        userData.admin_info = {
          admin_role: 'super_admin',
          permissions: ['read', 'write', 'delete', 'manage_users'],
          assigned_areas: ['all'],
        };
        break;
    }

    const user = await this.usersService.create(userData);

    const payload = {
      sub: user._id,
      email: user.email,
      roles: user.roles
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        roles: user.roles,
        customer_info: user.customer_info,
        seller_info: user.seller_info,
        admin_info: user.admin_info,
      },
    };
  }
}
