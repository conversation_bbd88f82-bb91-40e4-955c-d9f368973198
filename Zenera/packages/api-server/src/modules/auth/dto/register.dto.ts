import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength, IsEnum, IsOptional } from 'class-validator';
import { Role } from '../../users/enums/role.enum';

export class RegisterDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'password123' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ example: 'john_doe' })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ example: 'John' })
  @IsString()
  @MinLength(2)
  first_name: string;

  @ApiProperty({ example: 'Doe' })
  @IsString()
  @MinLength(2)
  last_name: string;

  @ApiProperty({ example: '+1234567890' })
  @IsString()
  @IsOptional()
  phone_number?: string;

  @ApiProperty({ example: 'customer', enum: Role })
  @IsEnum(Role)
  @IsOptional()
  role?: Role = Role.CUSTOMER;
}
