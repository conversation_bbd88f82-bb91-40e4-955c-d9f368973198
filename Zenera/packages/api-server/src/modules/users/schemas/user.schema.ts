import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Role } from '../enums/role.enum';
import * as bcryptjs from 'bcryptjs';

export enum UserStatus {
    ACTIVE = 'active',
    LOCKED = 'locked',
    SUSPENDED = 'suspended'
}

export interface ActivityLog {
    action: string;
    ip_address?: string;
    user_agent?: string;
    timestamp: Date;
    details?: any;
}

export interface User extends Document {
    email: string;
    password: string;
    username?: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    roles: Role[];
    status?: UserStatus;
    is_active?: boolean;
    is_verified?: boolean;
    avatar_url?: string;
    last_login_at?: Date;
    last_password_change?: Date;
    password_reset_token?: string;
    password_reset_expires?: Date;
    email_verification_token?: string;
    email_verification_expires?: Date;
    // Account security
    failed_login_attempts: number;
    last_failed_login_at?: Date;
    lock_until?: Date;
    // Role specific information
    customer_info?: {
        points: number;
        addresses: string[];
        default_address?: string;
    };
    seller_info?: {
        store_name: string;
        store_description?: string;
        store_logo?: string;
        store_banner?: string;
        is_approved: boolean;
        approved_at?: Date;
        rejection_reason?: string;
        rejected_at?: Date;
        store_addresses: string[];
        primary_address?: string;
        business_license?: string;
        tax_id?: string;
        bank_account?: {
            account_number: string;
            bank_name: string;
            account_holder: string;
        };
    };
    admin_info?: {
        admin_role: Role.ADMIN | Role.SUPER_ADMIN;
        permissions: string[];
        assigned_areas?: string[];
    };
    comparePassword(candidatePassword: string): Promise<boolean>;
}

// New Sub-schema for Refresh Tokens
@Schema({ _id: false })
export class StoredRefreshToken {
    @Prop({ required: true })
    token_hash: string;

    @Prop()
    device_id?: string;

    @Prop({ required: true })
    expires_at: Date;

    @Prop({ type: Date, default: Date.now })
    created_at: Date;

    @Prop({ type: Date })
    last_used_at?: Date;

    @Prop()
    ip_address?: string;

    @Prop()
    user_agent?: string;
}
export const StoredRefreshTokenSchema = SchemaFactory.createForClass(StoredRefreshToken);

export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
    @Prop({ required: true, unique: true })
    email: string;

    @Prop({ required: true })
    password: string;

    @Prop({ unique: true })
    username?: string;

    @Prop({ required: true })
    first_name: string;

    @Prop({ required: true })
    last_name: string;

    @Prop()
    phone_number?: string;

    @Prop({ type: [String], enum: Role, default: [Role.CUSTOMER] })
    roles: Role[];

    @Prop({ type: String, enum: UserStatus, default: UserStatus.ACTIVE })
    status?: UserStatus;

    @Prop({ default: true })
    is_active?: boolean;

    @Prop({ default: false })
    is_verified?: boolean;

    @Prop()
    avatar_url?: string;

    @Prop({ type: Date })
    last_login_at?: Date;

    @Prop({ type: Date })
    last_password_change?: Date;

    @Prop()
    password_reset_token?: string;

    @Prop({ type: Date })
    password_reset_expires?: Date;

    @Prop()
    email_verification_token?: string;

    @Prop({ type: Date })
    email_verification_expires?: Date;

    // Account security
    @Prop({ default: 0 })
    failed_login_attempts: number;

    @Prop({ type: Date })
    last_failed_login_at?: Date;

    @Prop({ type: Date })
    lock_until?: Date;

    // Role specific information
    @Prop({
        type: {
            points: { type: Number, default: 0 },
            addresses: [{ type: String }],
            default_address: String
        }
    })
    customer_info?: {
        points: number;
        addresses: string[];
        default_address?: string;
    };

    @Prop({
        type: {
            store_name: String,
            store_description: String,
            store_logo: String,
            store_banner: String,
            is_approved: { type: Boolean, default: false },
            approved_at: Date,
            rejection_reason: String,
            rejected_at: Date,
            store_addresses: [{ type: String }],
            primary_address: String,
            business_license: String,
            tax_id: String,
            bank_account: {
                account_number: String,
                bank_name: String,
                account_holder: String
            }
        }
    })
    seller_info?: {
        store_name: string;
        store_description?: string;
        store_logo?: string;
        store_banner?: string;
        is_approved: boolean;
        approved_at?: Date;
        rejection_reason?: string;
        rejected_at?: Date;
        store_addresses: string[];
        primary_address?: string;
        business_license?: string;
        tax_id?: string;
        bank_account?: {
            account_number: string;
            bank_name: string;
            account_holder: string;
        };
    };

    @Prop({
        type: {
            admin_role: { type: String, enum: [Role.ADMIN, Role.SUPER_ADMIN] },
            permissions: [{ type: String }],
            assigned_areas: [{ type: String }]
        }
    })
    admin_info?: {
        admin_role: Role.ADMIN | Role.SUPER_ADMIN;
        permissions: string[];
        assigned_areas?: string[];
    };

    @Prop({ type: [StoredRefreshTokenSchema], default: [] })
    refresh_tokens: StoredRefreshToken[];

    @Prop({ type: Date, default: Date.now })
    created_at: Date;

    @Prop({ type: Date, default: Date.now })
    updated_at: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Add indexes
UserSchema.index({ roles: 1 });
UserSchema.index({ status: 1 });
UserSchema.index({ is_active: 1 });
UserSchema.index({ is_verified: 1 });
UserSchema.index({ 'seller_info.is_approved': 1 });
UserSchema.index({ 'admin_info.admin_role': 1 });
UserSchema.index({ failed_login_attempts: 1 });
UserSchema.index({ lock_until: 1 });

// Add methods
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
    console.log('Comparing password...');
    try {
        const result = await bcryptjs.compare(candidatePassword, this.password);
        console.log('Password comparison result:', result);
        return result;
    } catch (error) {
        console.error('Error comparing password:', error);
        return false;
    }
};
