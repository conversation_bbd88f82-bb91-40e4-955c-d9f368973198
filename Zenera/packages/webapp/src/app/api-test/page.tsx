'use client';

import { useState } from 'react';
import { Button } from '@zenera/ui-components';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { api } from '@/lib/api';

export default function ApiTestPage() {
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testHealthCheck = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.health.check();
      setHealthStatus(result);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testProductsAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.products.getProducts({ limit: 5 });
      setProducts(result.products || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testAuthAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      // Test registration
      const timestamp = Date.now();
      const registerResult = await api.auth.register({
        email: `test${timestamp}@example.com`,
        password: 'password123',
        first_name: 'Test',
        last_name: 'User',
        username: `test${timestamp}`,
        role: 'customer',
      });
      
      console.log('Registration successful:', registerResult);
      
      // Test getting profile
      const profile = await api.auth.getProfile();
      console.log('Profile:', profile);
      
      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            API Integration Test
          </h1>
          <p className="text-lg text-muted-foreground">
            Day 5: Testing Frontend-Backend Connectivity
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="text-destructive">
                <strong>Error:</strong> {error}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Health Check Test */}
        <Card>
          <CardHeader>
            <CardTitle>Health Check Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testHealthCheck} 
              loading={loading}
              className="w-full"
            >
              Test Health Check
            </Button>
            
            {healthStatus && (
              <div className="p-4 bg-muted rounded-lg">
                <pre className="text-sm">
                  {JSON.stringify(healthStatus, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Products API Test */}
        <Card>
          <CardHeader>
            <CardTitle>Products API Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testProductsAPI} 
              loading={loading}
              className="w-full"
            >
              Test Products API
            </Button>
            
            {products.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-semibold">Products ({products.length}):</h3>
                <div className="p-4 bg-muted rounded-lg max-h-64 overflow-y-auto">
                  <pre className="text-sm">
                    {JSON.stringify(products, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Auth API Test */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication API Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testAuthAPI} 
              loading={loading}
              className="w-full"
            >
              Test Auth API (Register + Profile)
            </Button>
            
            <div className="text-sm text-muted-foreground">
              This will create a test user and fetch profile. Check browser console for results.
            </div>
          </CardContent>
        </Card>

        {/* API Client Info */}
        <Card>
          <CardHeader>
            <CardTitle>API Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL}</div>
              <div><strong>API Version:</strong> {process.env.NEXT_PUBLIC_API_VERSION}</div>
              <div><strong>Auth Token:</strong> {api.auth.getToken() ? 'Present' : 'Not set'}</div>
              <div><strong>Authenticated:</strong> {api.auth.isAuthenticated() ? 'Yes' : 'No'}</div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>1. <strong>Health Check:</strong> Tests basic API connectivity</p>
              <p>2. <strong>Products API:</strong> Tests product listing (may be empty initially)</p>
              <p>3. <strong>Auth API:</strong> Tests user registration and profile fetching</p>
              <p>4. Open browser console to see detailed API responses</p>
              <p>5. Check Network tab to see actual HTTP requests</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
