import { Button } from '@zenera/ui-components';
import { Input } from '@zenera/ui-components';
import { Card, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { ProductCard, CartItem } from '@zenera/ui-components';

export default function DemoPage() {
  // Sample data for testing
  const sampleProduct = {
    id: '1',
    name: 'Premium Wireless Headphones',
    price: 299.99,
    originalPrice: 399.99,
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    rating: 4.5,
    reviewCount: 128,
    badge: 'Best Seller',
    inStock: true,
  };

  const sampleCartItem = {
    id: '1',
    name: 'Premium Wireless Headphones',
    price: 299.99,
    originalPrice: 399.99,
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100&h=100&fit=crop',
    quantity: 2,
    maxQuantity: 10,
    variant: 'Black, Large',
    inStock: true,
  };

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-6xl mx-auto space-y-12">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            Zenera Design System Demo
          </h1>
          <p className="text-lg text-muted-foreground">
            Day 4: UI Components & Design System Foundation
          </p>
        </div>

        {/* Button Components */}
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Basic Variants</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="default">Default</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="link">Link</Button>
                <Button variant="destructive">Destructive</Button>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">E-commerce Variants</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="cart">Add to Cart</Button>
                <Button variant="buy">Buy Now</Button>
                <Button variant="wishlist">♡ Wishlist</Button>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Sizes & States</h3>
              <div className="flex flex-wrap items-center gap-4">
                <Button size="sm">Small</Button>
                <Button size="default">Default</Button>
                <Button size="lg">Large</Button>
                <Button loading>Loading</Button>
                <Button disabled>Disabled</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Input Components */}
        <Card>
          <CardHeader>
            <CardTitle>Input Components</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Input 
                  label="Default Input"
                  placeholder="Enter text..."
                />
                <Input 
                  label="Input with Error"
                  placeholder="Enter text..."
                  error="This field is required"
                />
                <Input 
                  label="Input with Success"
                  placeholder="Enter text..."
                  success="Looks good!"
                />
              </div>
              <div className="space-y-4">
                <Input 
                  label="Small Input"
                  size="sm"
                  placeholder="Small input..."
                />
                <Input 
                  label="Large Input"
                  size="lg"
                  placeholder="Large input..."
                />
                <Input 
                  label="Loading Input"
                  placeholder="Loading..."
                  loading
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Product Card */}
        <Card>
          <CardHeader>
            <CardTitle>Product Card Component</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductCard 
                product={sampleProduct}
                onAddToCart={(id) => console.log('Add to cart:', id)}
                onAddToWishlist={(id) => console.log('Add to wishlist:', id)}
                onQuickView={(id) => console.log('Quick view:', id)}
              />
              <ProductCard 
                product={{...sampleProduct, id: '2'}}
                variant="compact"
                onAddToCart={(id) => console.log('Add to cart:', id)}
              />
              <ProductCard 
                product={{...sampleProduct, id: '3'}}
                variant="featured"
                onAddToCart={(id) => console.log('Add to cart:', id)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Cart Item */}
        <Card>
          <CardHeader>
            <CardTitle>Cart Item Component</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <CartItem 
                item={sampleCartItem}
                onUpdateQuantity={(id, quantity) => console.log('Update quantity:', id, quantity)}
                onRemove={(id) => console.log('Remove item:', id)}
                onMoveToWishlist={(id) => console.log('Move to wishlist:', id)}
              />
              <CartItem 
                item={{...sampleCartItem, id: '2', inStock: false}}
                variant="compact"
                onUpdateQuantity={(id, quantity) => console.log('Update quantity:', id, quantity)}
                onRemove={(id) => console.log('Remove item:', id)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-muted-foreground">
          <p>✅ Day 4 Progress: Basic UI Components Foundation Complete</p>
          <p>Next: Medoo Integration & Form System (Day 5)</p>
        </div>
      </div>
    </div>
  );
}
