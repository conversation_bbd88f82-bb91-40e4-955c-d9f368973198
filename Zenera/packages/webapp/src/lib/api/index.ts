/**
 * API Services Index
 * Centralized export cho tất cả API services
 */

// Export API client
export { apiClient, API_ENDPOINTS } from '../api-client';
export type { ApiResponse, ApiError, RequestConfig } from '../api-client';
export { ApiClientError, NetworkError } from '../api-client';

// Import APIs for re-export
import { authApi } from './auth';
import { productsApi } from './products';

// Export Auth API
export { authApi } from './auth';
export type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
} from './auth';

// Export Products API
export { productsApi } from './products';
export type {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  ProductsQuery,
  ProductsResponse,
  Category,
} from './products';

// Health check utility
export const healthApi = {
  async check(): Promise<{ status: string; message: string; timestamp: string }> {
    return apiClient.get(API_ENDPOINTS.HEALTH);
  },
};

// Export all APIs as a single object
export const api = {
  auth: authApi,
  products: productsApi,
  health: healthApi,
};
