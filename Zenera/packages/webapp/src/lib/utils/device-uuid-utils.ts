/**
 * Device UUID Utilities
 * Extracted và adapted từ Medoo cho Zenera
 */

import Cookies from 'js-cookie';
import { v4 as uuidV4, validate } from 'uuid';

// Constants
export const DEVICE_UUID_NAMESPACE = 'zenera_device_uuid';
export const DEVICE_UUID_EXPIRES_DAYS = 365; // 1 year

/**
 * Device UUID Management
 * Adapted từ Medoo DeviceUuidUtils
 */
export const DeviceUuidUtils = {
  /**
   * Get device UUID từ cookies
   */
  getDeviceUuid(): string {
    if (typeof window !== 'undefined') {
      return Cookies.get(DEVICE_UUID_NAMESPACE) || '';
    }
    return '';
  },

  /**
   * Set device UUID vào cookies
   */
  setDeviceUuid(newDeviceUuid: string): void {
    if (!validate(newDeviceUuid) || typeof window === 'undefined') {
      return;
    }
    Cookies.set(DEVICE_UUID_NAMESPACE, newDeviceUuid, { 
      expires: DEVICE_UUID_EXPIRES_DAYS 
    });
  },

  /**
   * Generate và set new device UUID nếu chưa có
   */
  handleCheckDeviceUuid(): string {
    const deviceUuid = this.getDeviceUuid();

    // Nếu đã có UUID hợp lệ, return nó
    if (validate(deviceUuid)) {
      return deviceUuid;
    }

    // Generate UUID mới
    const newDeviceUuid = uuidV4();
    this.setDeviceUuid(newDeviceUuid);
    return newDeviceUuid;
  },

  /**
   * Validate device UUID
   */
  isValidDeviceUuid(uuid?: string): boolean {
    const deviceUuid = uuid || this.getDeviceUuid();
    return validate(deviceUuid);
  },

  /**
   * Clear device UUID
   */
  clearDeviceUuid(): void {
    if (typeof window !== 'undefined') {
      Cookies.remove(DEVICE_UUID_NAMESPACE);
    }
  },

  /**
   * Get or create device UUID
   */
  getOrCreateDeviceUuid(): string {
    return this.handleCheckDeviceUuid();
  },

  /**
   * Get device info object
   */
  getDeviceInfo(): {
    uuid: string;
    userAgent: string;
    platform: string;
    language: string;
    timestamp: number;
  } {
    if (typeof window === 'undefined') {
      return {
        uuid: '',
        userAgent: '',
        platform: '',
        language: '',
        timestamp: Date.now(),
      };
    }

    return {
      uuid: this.getOrCreateDeviceUuid(),
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      timestamp: Date.now(),
    };
  },

  /**
   * Initialize device UUID on app start
   */
  initialize(): string {
    return this.handleCheckDeviceUuid();
  },
};
