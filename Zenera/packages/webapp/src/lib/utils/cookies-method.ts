/**
 * Cookie Management Utilities
 * Extracted và adapted từ Medoo cho Zenera
 */

import dayjs from 'dayjs';
import Cookies from 'js-cookie';

// Constants
export const TOKEN_EXPIRED_DAYS = 7; // 7 days
export const REFRESH_TOKEN_EXPIRED_DAYS = 30; // 30 days

/**
 * Get expiration days từ cookies
 */
const getDayNumberFromExpireAtCookies = (): number => {
  const expiresIn = Number(Cookies.get('expiredAt') ?? 0);

  if (expiresIn) {
    return Math.floor(dayjs(expiresIn).diff(dayjs(), 'day'));
  }

  return TOKEN_EXPIRED_DAYS;
};

/**
 * Set cookie với expiration date
 */
const setCookiesWithExpires = (key: string, value: any, modifiedRate = 1): void => {
  const expires = getDayNumberFromExpireAtCookies();
  Cookies.set(key, value, { expires: expires * modifiedRate });
};

// Refresh token expiration rate
const RefreshTokenOnToken = REFRESH_TOKEN_EXPIRED_DAYS / TOKEN_EXPIRED_DAYS;

/**
 * Zenera Cookie Management Methods
 * Adapted từ Medoo HCookiesMethod
 */
export const ZeneraCookiesMethod = {
  /**
   * Set access token
   */
  setAccessToken: (accessToken: string): void => {
    setCookiesWithExpires('zenera_token', accessToken);
  },

  /**
   * Set refresh token
   */
  setRefreshToken: (refreshToken: string): void => {
    setCookiesWithExpires('zenera_refresh_token', refreshToken, RefreshTokenOnToken);
  },

  /**
   * Set user data
   */
  setUser: (user: any): void => {
    setCookiesWithExpires('zenera_user', JSON.stringify(user || '{}'), RefreshTokenOnToken);
  },

  /**
   * Set user permissions
   */
  setPermissions: (permissions: string[]): void => {
    setCookiesWithExpires('zenera_permissions', JSON.stringify(permissions || []), RefreshTokenOnToken);
  },

  /**
   * Set token expiration
   */
  setExpiredAt: (expiresIn: number): void => {
    const currentTime: number = new Date().getTime();
    const expiredAt = currentTime + parseInt(`${(expiresIn - 60) * 1000}`);
    const expiredDay = Math.floor(expiresIn / 86400);

    Cookies.set('zenera_expires_in', expiresIn.toString(), { expires: expiredDay });
    Cookies.set('zenera_expired_at', `${expiredAt}`, { expires: expiredDay * RefreshTokenOnToken });
  },

  /**
   * Get access token
   */
  getAccessToken: (): string | undefined => {
    return Cookies.get('zenera_token');
  },

  /**
   * Get refresh token
   */
  getRefreshToken: (): string | undefined => {
    return Cookies.get('zenera_refresh_token');
  },

  /**
   * Get user data
   */
  getUser: (): any => {
    const userStr = Cookies.get('zenera_user');
    try {
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  },

  /**
   * Get user permissions
   */
  getPermissions: (): string[] => {
    const permissionsStr = Cookies.get('zenera_permissions');
    try {
      return permissionsStr ? JSON.parse(permissionsStr) : [];
    } catch {
      return [];
    }
  },

  /**
   * Get token expiration
   */
  getExpiredAt: (): number => {
    const expiredAt = Cookies.get('zenera_expired_at');
    return expiredAt ? parseInt(expiredAt) : 0;
  },

  /**
   * Check if token is expired
   */
  isTokenExpired: (): boolean => {
    const expiredAt = ZeneraCookiesMethod.getExpiredAt();
    if (!expiredAt) return true;
    
    const currentTime = new Date().getTime();
    return currentTime >= expiredAt;
  },

  /**
   * Clear all auth cookies
   */
  clearAuthCookies: (): void => {
    Cookies.remove('zenera_token');
    Cookies.remove('zenera_refresh_token');
    Cookies.remove('zenera_user');
    Cookies.remove('zenera_permissions');
    Cookies.remove('zenera_expires_in');
    Cookies.remove('zenera_expired_at');
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    const token = ZeneraCookiesMethod.getAccessToken();
    const isExpired = ZeneraCookiesMethod.isTokenExpired();
    return !!(token && !isExpired);
  },
};

/**
 * Authentication Utilities
 * Adapted từ Medoo AuthenticationUtils
 */
export const AuthenticationUtils = {
  /**
   * Reset all authentication cookies
   */
  resetCookies: (): void => {
    ZeneraCookiesMethod.clearAuthCookies();
  },

  /**
   * Handle login success
   * Adapted từ Medoo onLoginSuccess
   */
  onLoginSuccess: (response: any, callback?: () => void): void => {
    const {
      user,
      access_token,
      refresh_token,
      expires_in = 0,
      permissions = [],
    } = response || {};

    // Set cookies
    ZeneraCookiesMethod.setExpiredAt(expires_in);
    ZeneraCookiesMethod.setAccessToken(access_token);
    ZeneraCookiesMethod.setRefreshToken(refresh_token);
    ZeneraCookiesMethod.setUser(user);
    ZeneraCookiesMethod.setPermissions(permissions);

    // Execute callback
    if (callback) {
      callback();
    }
  },

  /**
   * Handle logout
   */
  onLogout: (callback?: () => void): void => {
    AuthenticationUtils.resetCookies();
    
    if (callback) {
      callback();
    }
  },

  /**
   * Get current auth state từ cookies
   */
  getAuthState: () => {
    return {
      token: ZeneraCookiesMethod.getAccessToken(),
      refreshToken: ZeneraCookiesMethod.getRefreshToken(),
      user: ZeneraCookiesMethod.getUser(),
      permissions: ZeneraCookiesMethod.getPermissions(),
      isAuthenticated: ZeneraCookiesMethod.isAuthenticated(),
      isExpired: ZeneraCookiesMethod.isTokenExpired(),
      expiredAt: ZeneraCookiesMethod.getExpiredAt(),
    };
  },
};
