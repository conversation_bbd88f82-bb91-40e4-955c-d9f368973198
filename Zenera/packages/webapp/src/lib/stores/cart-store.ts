/**
 * Cart Store
 * Adapted từ Medoo cart management patterns cho Zenera E-commerce
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { api } from '@/lib/api';

// Types
export interface CartItem {
  id: string;
  productId: string;
  variantId?: string;
  name: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  image?: string;
  sku?: string;
  attributes?: Record<string, string>;
  seller: {
    id: string;
    name: string;
  };
  maxQuantity?: number;
  isAvailable: boolean;
}

export interface CartSummary {
  subtotal: number;
  discount: number;
  shipping: number;
  tax: number;
  total: number;
  itemCount: number;
  uniqueItemCount: number;
}

export interface CartStore {
  // State
  items: CartItem[];
  summary: CartSummary;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
  
  // Guest cart support
  guestCartId?: string;
  
  // Actions
  addItem: (product: any, quantity?: number, variantId?: string) => Promise<void>;
  updateQuantity: (itemId: string, quantity: number) => Promise<void>;
  removeItem: (itemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  
  // Bulk operations
  addMultipleItems: (items: Array<{ product: any; quantity: number; variantId?: string }>) => Promise<void>;
  removeMultipleItems: (itemIds: string[]) => Promise<void>;
  
  // Cart management
  syncWithServer: () => Promise<void>;
  mergeGuestCart: (guestCartId: string) => Promise<void>;
  calculateSummary: () => void;
  
  // Validation
  validateCart: () => Promise<{ isValid: boolean; errors: string[] }>;
  checkItemAvailability: (itemId: string) => Promise<boolean>;
  
  // Utilities
  getItemById: (itemId: string) => CartItem | undefined;
  getItemsByseller: (sellerId: string) => CartItem[];
  getTotalBySeller: (sellerId: string) => number;
  
  // Persistence
  hydrate: () => void;
  reset: () => void;
}

const initialSummary: CartSummary = {
  subtotal: 0,
  discount: 0,
  shipping: 0,
  tax: 0,
  total: 0,
  itemCount: 0,
  uniqueItemCount: 0,
};

/**
 * Cart Store Implementation
 * Adapted từ Medoo cart patterns với Zustand
 */
export const useCartStore = create<CartStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      items: [],
      summary: initialSummary,
      isLoading: false,
      error: null,
      lastUpdated: Date.now(),
      
      // Add item to cart
      addItem: async (product, quantity = 1, variantId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const existingItemIndex = get().items.findIndex(
            item => item.productId === product.id && item.variantId === variantId
          );

          if (existingItemIndex >= 0) {
            // Update existing item
            await get().updateQuantity(
              get().items[existingItemIndex].id,
              get().items[existingItemIndex].quantity + quantity
            );
          } else {
            // Add new item
            const newItem: CartItem = {
              id: `${product.id}-${variantId || 'default'}-${Date.now()}`,
              productId: product.id,
              variantId,
              name: product.name || product.title,
              price: product.price,
              originalPrice: product.originalPrice,
              quantity,
              image: product.images?.[0] || product.image,
              sku: product.sku,
              attributes: product.attributes,
              seller: {
                id: product.seller?.id || product.sellerId,
                name: product.seller?.name || product.sellerName,
              },
              maxQuantity: product.stock || product.maxQuantity,
              isAvailable: product.isAvailable !== false,
            };

            set((state) => {
              state.items.push(newItem);
              state.lastUpdated = Date.now();
            });

            // Sync with server if user is authenticated
            await get().syncWithServer();
          }

          get().calculateSummary();
        } catch (error: any) {
          set((state) => {
            state.error = error.message || 'Failed to add item to cart';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Update item quantity
      updateQuantity: async (itemId, quantity) => {
        if (quantity <= 0) {
          await get().removeItem(itemId);
          return;
        }

        set((state) => {
          const item = state.items.find(item => item.id === itemId);
          if (item) {
            // Check max quantity
            if (item.maxQuantity && quantity > item.maxQuantity) {
              state.error = `Maximum quantity for ${item.name} is ${item.maxQuantity}`;
              return;
            }
            
            item.quantity = quantity;
            state.lastUpdated = Date.now();
            state.error = null;
          }
        });

        get().calculateSummary();
        await get().syncWithServer();
      },

      // Remove item from cart
      removeItem: async (itemId) => {
        set((state) => {
          state.items = state.items.filter(item => item.id !== itemId);
          state.lastUpdated = Date.now();
        });

        get().calculateSummary();
        await get().syncWithServer();
      },

      // Clear entire cart
      clearCart: async () => {
        set((state) => {
          state.items = [];
          state.summary = initialSummary;
          state.lastUpdated = Date.now();
          state.error = null;
        });

        await get().syncWithServer();
      },

      // Add multiple items
      addMultipleItems: async (items) => {
        set((state) => {
          state.isLoading = true;
        });

        try {
          for (const { product, quantity, variantId } of items) {
            await get().addItem(product, quantity, variantId);
          }
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Remove multiple items
      removeMultipleItems: async (itemIds) => {
        set((state) => {
          state.items = state.items.filter(item => !itemIds.includes(item.id));
          state.lastUpdated = Date.now();
        });

        get().calculateSummary();
        await get().syncWithServer();
      },

      // Sync with server
      syncWithServer: async () => {
        try {
          // Only sync if user is authenticated
          // Implementation depends on auth state
          const cartData = {
            items: get().items,
            lastUpdated: get().lastUpdated,
          };

          // TODO: Implement API call to sync cart
          // await api.cart.sync(cartData);
        } catch (error) {
          console.error('Failed to sync cart with server:', error);
        }
      },

      // Merge guest cart with user cart
      mergeGuestCart: async (guestCartId) => {
        try {
          set((state) => {
            state.isLoading = true;
          });

          // TODO: Implement guest cart merge
          // const guestCart = await api.cart.getGuestCart(guestCartId);
          // await get().addMultipleItems(guestCart.items);

          set((state) => {
            state.guestCartId = undefined;
          });
        } catch (error: any) {
          set((state) => {
            state.error = error.message || 'Failed to merge guest cart';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },

      // Calculate cart summary
      calculateSummary: () => {
        set((state) => {
          const items = state.items;
          
          const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
          const uniqueItemCount = items.length;
          
          // TODO: Calculate discount, shipping, tax based on business rules
          const discount = 0;
          const shipping = subtotal > 100 ? 0 : 10; // Free shipping over $100
          const tax = subtotal * 0.1; // 10% tax
          const total = subtotal - discount + shipping + tax;

          state.summary = {
            subtotal,
            discount,
            shipping,
            tax,
            total,
            itemCount,
            uniqueItemCount,
          };
        });
      },

      // Validate cart
      validateCart: async () => {
        const errors: string[] = [];
        const items = get().items;

        for (const item of items) {
          // Check availability
          if (!item.isAvailable) {
            errors.push(`${item.name} is no longer available`);
          }

          // Check stock
          if (item.maxQuantity && item.quantity > item.maxQuantity) {
            errors.push(`${item.name} quantity exceeds available stock`);
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Check item availability
      checkItemAvailability: async (itemId) => {
        const item = get().getItemById(itemId);
        if (!item) return false;

        try {
          // TODO: Check with API
          // const availability = await api.products.checkAvailability(item.productId, item.variantId);
          // return availability.isAvailable;
          return item.isAvailable;
        } catch (error) {
          return false;
        }
      },

      // Utility functions
      getItemById: (itemId) => {
        return get().items.find(item => item.id === itemId);
      },

      getItemsByseller: (sellerId) => {
        return get().items.filter(item => item.seller.id === sellerId);
      },

      getTotalBySeller: (sellerId) => {
        return get().getItemsByseller(sellerId)
          .reduce((sum, item) => sum + (item.price * item.quantity), 0);
      },

      // Persistence
      hydrate: () => {
        get().calculateSummary();
      },

      reset: () => {
        set((state) => {
          state.items = [];
          state.summary = initialSummary;
          state.isLoading = false;
          state.error = null;
          state.lastUpdated = Date.now();
          state.guestCartId = undefined;
        });
      },
    })),
    {
      name: 'zenera-cart',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        items: state.items,
        lastUpdated: state.lastUpdated,
        guestCartId: state.guestCartId,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.hydrate();
        }
      },
    }
  )
);
