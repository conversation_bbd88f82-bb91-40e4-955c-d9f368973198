/**
 * User Preferences Store
 * Adapted từ Medoo user preferences patterns cho Zenera E-commerce
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types
export interface Theme {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  accentColor: string;
  borderRadius: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  fontFamily: 'inter' | 'roboto' | 'poppins' | 'system';
}

export interface Language {
  code: string;
  name: string;
  flag: string;
}

export interface Currency {
  code: string;
  symbol: string;
  name: string;
}

export interface NotificationSettings {
  email: {
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
    security: boolean;
  };
  push: {
    orderUpdates: boolean;
    promotions: boolean;
    newProducts: boolean;
    priceDrops: boolean;
  };
  sms: {
    orderUpdates: boolean;
    security: boolean;
  };
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'friends';
  showOnlineStatus: boolean;
  allowDataCollection: boolean;
  allowPersonalization: boolean;
  allowThirdPartySharing: boolean;
}

export interface ShoppingPreferences {
  defaultShippingAddress?: string;
  defaultBillingAddress?: string;
  preferredPaymentMethod?: string;
  autoSaveToWishlist: boolean;
  showOutOfStock: boolean;
  defaultSortBy: 'relevance' | 'price_low' | 'price_high' | 'newest' | 'rating';
  itemsPerPage: 12 | 24 | 48 | 96;
  viewMode: 'grid' | 'list';
}

export interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  highContrast: boolean;
  reduceMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
}

export interface PreferencesStore {
  // Core preferences
  theme: Theme;
  language: Language;
  currency: Currency;
  timezone: string;
  
  // Settings
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  shopping: ShoppingPreferences;
  accessibility: AccessibilitySettings;
  
  // UI State
  sidebarCollapsed: boolean;
  compactMode: boolean;
  showTutorials: boolean;
  
  // Recently viewed
  recentlyViewed: string[]; // Product IDs
  recentSearches: string[];
  
  // Favorites
  favoriteCategories: string[];
  favoriteSellers: string[];
  
  // Actions - Theme
  setTheme: (theme: Partial<Theme>) => void;
  toggleThemeMode: () => void;
  resetTheme: () => void;
  
  // Actions - Language & Currency
  setLanguage: (language: Language) => void;
  setCurrency: (currency: Currency) => void;
  setTimezone: (timezone: string) => void;
  
  // Actions - Settings
  updateNotifications: (notifications: Partial<NotificationSettings>) => void;
  updatePrivacy: (privacy: Partial<PrivacySettings>) => void;
  updateShopping: (shopping: Partial<ShoppingPreferences>) => void;
  updateAccessibility: (accessibility: Partial<AccessibilitySettings>) => void;
  
  // Actions - UI State
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleCompactMode: () => void;
  setShowTutorials: (show: boolean) => void;
  
  // Actions - Recently viewed
  addRecentlyViewed: (productId: string) => void;
  clearRecentlyViewed: () => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  
  // Actions - Favorites
  addFavoriteCategory: (categoryId: string) => void;
  removeFavoriteCategory: (categoryId: string) => void;
  addFavoriteSeller: (sellerId: string) => void;
  removeFavoriteSeller: (sellerId: string) => void;
  
  // Utilities
  getEffectiveTheme: () => 'light' | 'dark';
  isRTL: () => boolean;
  
  // Reset
  reset: () => void;
  resetToDefaults: () => void;
}

// Default values
const defaultTheme: Theme = {
  mode: 'system',
  primaryColor: '#3b82f6',
  accentColor: '#10b981',
  borderRadius: 'md',
  fontFamily: 'inter',
};

const defaultLanguage: Language = {
  code: 'en',
  name: 'English',
  flag: '🇺🇸',
};

const defaultCurrency: Currency = {
  code: 'USD',
  symbol: '$',
  name: 'US Dollar',
};

const defaultNotifications: NotificationSettings = {
  email: {
    orderUpdates: true,
    promotions: false,
    newsletter: false,
    security: true,
  },
  push: {
    orderUpdates: true,
    promotions: false,
    newProducts: false,
    priceDrops: false,
  },
  sms: {
    orderUpdates: false,
    security: true,
  },
};

const defaultPrivacy: PrivacySettings = {
  profileVisibility: 'private',
  showOnlineStatus: false,
  allowDataCollection: true,
  allowPersonalization: true,
  allowThirdPartySharing: false,
};

const defaultShopping: ShoppingPreferences = {
  autoSaveToWishlist: false,
  showOutOfStock: true,
  defaultSortBy: 'relevance',
  itemsPerPage: 24,
  viewMode: 'grid',
};

const defaultAccessibility: AccessibilitySettings = {
  fontSize: 'medium',
  highContrast: false,
  reduceMotion: false,
  screenReader: false,
  keyboardNavigation: false,
};

/**
 * User Preferences Store Implementation
 * Adapted từ Medoo preferences patterns với Zustand
 */
export const usePreferencesStore = create<PreferencesStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      theme: defaultTheme,
      language: defaultLanguage,
      currency: defaultCurrency,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      
      notifications: defaultNotifications,
      privacy: defaultPrivacy,
      shopping: defaultShopping,
      accessibility: defaultAccessibility,
      
      sidebarCollapsed: false,
      compactMode: false,
      showTutorials: true,
      
      recentlyViewed: [],
      recentSearches: [],
      
      favoriteCategories: [],
      favoriteSellers: [],
      
      // Theme actions
      setTheme: (theme) => {
        set((state) => {
          state.theme = { ...state.theme, ...theme };
        });
      },

      toggleThemeMode: () => {
        set((state) => {
          const modes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
          const currentIndex = modes.indexOf(state.theme.mode);
          const nextIndex = (currentIndex + 1) % modes.length;
          state.theme.mode = modes[nextIndex];
        });
      },

      resetTheme: () => {
        set((state) => {
          state.theme = defaultTheme;
        });
      },

      // Language & Currency actions
      setLanguage: (language) => {
        set((state) => {
          state.language = language;
        });
      },

      setCurrency: (currency) => {
        set((state) => {
          state.currency = currency;
        });
      },

      setTimezone: (timezone) => {
        set((state) => {
          state.timezone = timezone;
        });
      },

      // Settings actions
      updateNotifications: (notifications) => {
        set((state) => {
          state.notifications = {
            ...state.notifications,
            ...notifications,
            email: { ...state.notifications.email, ...notifications.email },
            push: { ...state.notifications.push, ...notifications.push },
            sms: { ...state.notifications.sms, ...notifications.sms },
          };
        });
      },

      updatePrivacy: (privacy) => {
        set((state) => {
          state.privacy = { ...state.privacy, ...privacy };
        });
      },

      updateShopping: (shopping) => {
        set((state) => {
          state.shopping = { ...state.shopping, ...shopping };
        });
      },

      updateAccessibility: (accessibility) => {
        set((state) => {
          state.accessibility = { ...state.accessibility, ...accessibility };
        });
      },

      // UI State actions
      toggleSidebar: () => {
        set((state) => {
          state.sidebarCollapsed = !state.sidebarCollapsed;
        });
      },

      setSidebarCollapsed: (collapsed) => {
        set((state) => {
          state.sidebarCollapsed = collapsed;
        });
      },

      toggleCompactMode: () => {
        set((state) => {
          state.compactMode = !state.compactMode;
        });
      },

      setShowTutorials: (show) => {
        set((state) => {
          state.showTutorials = show;
        });
      },

      // Recently viewed actions
      addRecentlyViewed: (productId) => {
        set((state) => {
          // Remove if already exists
          state.recentlyViewed = state.recentlyViewed.filter(id => id !== productId);
          // Add to beginning
          state.recentlyViewed.unshift(productId);
          // Keep only last 20 items
          state.recentlyViewed = state.recentlyViewed.slice(0, 20);
        });
      },

      clearRecentlyViewed: () => {
        set((state) => {
          state.recentlyViewed = [];
        });
      },

      addRecentSearch: (query) => {
        if (!query.trim()) return;
        
        set((state) => {
          // Remove if already exists
          state.recentSearches = state.recentSearches.filter(q => q !== query);
          // Add to beginning
          state.recentSearches.unshift(query);
          // Keep only last 10 searches
          state.recentSearches = state.recentSearches.slice(0, 10);
        });
      },

      clearRecentSearches: () => {
        set((state) => {
          state.recentSearches = [];
        });
      },

      // Favorites actions
      addFavoriteCategory: (categoryId) => {
        set((state) => {
          if (!state.favoriteCategories.includes(categoryId)) {
            state.favoriteCategories.push(categoryId);
          }
        });
      },

      removeFavoriteCategory: (categoryId) => {
        set((state) => {
          state.favoriteCategories = state.favoriteCategories.filter(id => id !== categoryId);
        });
      },

      addFavoriteSeller: (sellerId) => {
        set((state) => {
          if (!state.favoriteSellers.includes(sellerId)) {
            state.favoriteSellers.push(sellerId);
          }
        });
      },

      removeFavoriteSeller: (sellerId) => {
        set((state) => {
          state.favoriteSellers = state.favoriteSellers.filter(id => id !== sellerId);
        });
      },

      // Utilities
      getEffectiveTheme: () => {
        const theme = get().theme;
        if (theme.mode === 'system') {
          return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        return theme.mode;
      },

      isRTL: () => {
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return rtlLanguages.includes(get().language.code);
      },

      // Reset actions
      reset: () => {
        set((state) => {
          state.theme = defaultTheme;
          state.language = defaultLanguage;
          state.currency = defaultCurrency;
          state.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          state.notifications = defaultNotifications;
          state.privacy = defaultPrivacy;
          state.shopping = defaultShopping;
          state.accessibility = defaultAccessibility;
          state.sidebarCollapsed = false;
          state.compactMode = false;
          state.showTutorials = true;
          state.recentlyViewed = [];
          state.recentSearches = [];
          state.favoriteCategories = [];
          state.favoriteSellers = [];
        });
      },

      resetToDefaults: () => {
        get().reset();
      },
    })),
    {
      name: 'zenera-preferences',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        currency: state.currency,
        timezone: state.timezone,
        notifications: state.notifications,
        privacy: state.privacy,
        shopping: state.shopping,
        accessibility: state.accessibility,
        sidebarCollapsed: state.sidebarCollapsed,
        compactMode: state.compactMode,
        showTutorials: state.showTutorials,
        recentlyViewed: state.recentlyViewed,
        recentSearches: state.recentSearches,
        favoriteCategories: state.favoriteCategories,
        favoriteSellers: state.favoriteSellers,
      }),
    }
  )
);
