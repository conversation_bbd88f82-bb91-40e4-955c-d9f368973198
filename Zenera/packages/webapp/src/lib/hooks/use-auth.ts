/**
 * Auth Hooks
 * Extracted và adapted từ Medoo cho Zenera
 */

import { useEffect } from 'react';
import { useAuthStore } from '../stores/auth-store';
import type { LoginRequest, RegisterRequest } from '../api/auth';

/**
 * Main auth hook
 * Adapted từ Medoo useAuth
 */
export const useAuth = () => {
  const {
    // State
    user,
    token,
    refreshToken,
    isAuthenticated,
    userPermissions,
    userRoles,
    isLoading,
    isInitializing,
    deviceUuid,
    
    // Actions
    login,
    register,
    logout,
    refreshAuth,
    getProfile,
    initialize,
    
    // Permission helpers
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    
    // Internal
    setUser,
    setToken,
    setLoading,
    clearAuth,
  } = useAuthStore();

  // Initialize auth on mount
  useEffect(() => {
    if (isInitializing) {
      initialize();
    }
  }, [initialize, isInitializing]);

  return {
    // State
    user,
    token,
    refreshToken,
    isAuthenticated,
    userPermissions,
    userRoles,
    isLoading,
    isInitializing,
    deviceUuid,
    
    // Actions
    login,
    register,
    logout,
    refreshAuth,
    getProfile,
    
    // Permission helpers
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    
    // Convenience methods
    isAdmin: () => hasRole('admin') || hasRole('super_admin'),
    isSeller: () => hasRole('seller'),
    isCustomer: () => hasRole('customer'),
    
    // Internal (for advanced usage)
    setUser,
    setToken,
    setLoading,
    clearAuth,
  };
};

/**
 * Permission hook
 * Adapted từ Medoo usePermission
 */
export const usePermission = (permissionCode: string): boolean => {
  const { hasPermission } = useAuth();
  return hasPermission(permissionCode);
};

/**
 * Multiple permissions hook
 * Adapted từ Medoo usePermissions
 */
export const usePermissions = (permissionCodes: string[]): boolean => {
  const { hasAnyPermission } = useAuth();
  return hasAnyPermission(permissionCodes);
};

/**
 * All permissions hook
 */
export const useAllPermissions = (permissionCodes: string[]): boolean => {
  const { hasAllPermissions } = useAuth();
  return hasAllPermissions(permissionCodes);
};

/**
 * Role-based hook
 */
export const useRole = (role: string): boolean => {
  const { hasRole } = useAuth();
  return hasRole(role);
};

/**
 * Admin check hook
 */
export const useIsAdmin = (): boolean => {
  const { isAdmin } = useAuth();
  return isAdmin();
};

/**
 * Seller check hook
 */
export const useIsSeller = (): boolean => {
  const { isSeller } = useAuth();
  return isSeller();
};

/**
 * Customer check hook
 */
export const useIsCustomer = (): boolean => {
  const { isCustomer } = useAuth();
  return isCustomer();
};

/**
 * Login hook với loading state
 */
export const useLogin = () => {
  const { login, isLoading } = useAuth();
  
  const handleLogin = async (credentials: LoginRequest) => {
    try {
      const response = await login(credentials);
      return response;
    } catch (error) {
      throw error;
    }
  };

  return {
    login: handleLogin,
    isLoading,
  };
};

/**
 * Register hook với loading state
 */
export const useRegister = () => {
  const { register, isLoading } = useAuth();
  
  const handleRegister = async (userData: RegisterRequest) => {
    try {
      const response = await register(userData);
      return response;
    } catch (error) {
      throw error;
    }
  };

  return {
    register: handleRegister,
    isLoading,
  };
};

/**
 * Logout hook
 */
export const useLogout = () => {
  const { logout, isLoading } = useAuth();
  
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with logout even if API fails
    }
  };

  return {
    logout: handleLogout,
    isLoading,
  };
};

/**
 * Auth guard hook cho protected routes
 */
export const useAuthGuard = (options?: {
  redirectTo?: string;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAll?: boolean; // true = all permissions/roles required, false = any
}) => {
  const { 
    isAuthenticated, 
    isInitializing, 
    hasAnyPermission, 
    hasAllPermissions,
    hasRole 
  } = useAuth();

  const {
    requiredPermissions = [],
    requiredRoles = [],
    requireAll = false,
  } = options || {};

  // Check authentication
  if (isInitializing) {
    return { canAccess: false, isLoading: true, reason: 'initializing' };
  }

  if (!isAuthenticated) {
    return { canAccess: false, isLoading: false, reason: 'not_authenticated' };
  }

  // Check permissions
  if (requiredPermissions.length > 0) {
    const hasPermissions = requireAll 
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);
    
    if (!hasPermissions) {
      return { canAccess: false, isLoading: false, reason: 'insufficient_permissions' };
    }
  }

  // Check roles
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requireAll
      ? requiredRoles.every(role => hasRole(role))
      : requiredRoles.some(role => hasRole(role));
    
    if (!hasRequiredRole) {
      return { canAccess: false, isLoading: false, reason: 'insufficient_role' };
    }
  }

  return { canAccess: true, isLoading: false, reason: null };
};

/**
 * Cookie authentication hook
 * Adapted từ Medoo useSetCookieAuthentication
 */
export const useSetCookieAuthentication = () => {
  const { setUser, setToken } = useAuth();
  
  return (response: any) => {
    const {
      user,
      access_token,
      permissions = [],
    } = response || {};
    
    // Update store state
    setUser(user);
    setToken(access_token);
    
    // Handle login success sẽ được xử lý trong store
  };
};
