/**
 * Auth Provider Component
 * Adapted từ Medoo AuthProvider cho Zenera
 */

'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuth } from '../hooks/use-auth';
import { DeviceUuidUtils } from '../utils/device-uuid-utils';

// Auth Context Interface
interface AuthContextType {
  // Re-export auth hook values
  user: ReturnType<typeof useAuth>['user'];
  token: ReturnType<typeof useAuth>['token'];
  isAuthenticated: ReturnType<typeof useAuth>['isAuthenticated'];
  isLoading: ReturnType<typeof useAuth>['isLoading'];
  isInitializing: ReturnType<typeof useAuth>['isInitializing'];
  userPermissions: ReturnType<typeof useAuth>['userPermissions'];
  userRoles: ReturnType<typeof useAuth>['userRoles'];
  deviceUuid: ReturnType<typeof useAuth>['deviceUuid'];
  
  // Actions
  login: ReturnType<typeof useAuth>['login'];
  register: ReturnType<typeof useAuth>['register'];
  logout: ReturnType<typeof useAuth>['logout'];
  refreshAuth: ReturnType<typeof useAuth>['refreshAuth'];
  getProfile: ReturnType<typeof useAuth>['getProfile'];
  
  // Permission helpers
  hasPermission: ReturnType<typeof useAuth>['hasPermission'];
  hasAnyPermission: ReturnType<typeof useAuth>['hasAnyPermission'];
  hasAllPermissions: ReturnType<typeof useAuth>['hasAllPermissions'];
  hasRole: ReturnType<typeof useAuth>['hasRole'];
  isAdmin: ReturnType<typeof useAuth>['isAdmin'];
  isSeller: ReturnType<typeof useAuth>['isSeller'];
  isCustomer: ReturnType<typeof useAuth>['isCustomer'];
}

// Create Auth Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Props
interface AuthProviderProps {
  children: ReactNode;
  cookies?: Record<string, string>; // For SSR support
}

/**
 * Auth Provider Component
 * Provides authentication context to the app
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ 
  children, 
  cookies = {} 
}) => {
  const auth = useAuth();

  // Initialize device UUID on mount
  useEffect(() => {
    DeviceUuidUtils.initialize();
  }, []);

  // Handle cookie changes (for SSR/hydration)
  useEffect(() => {
    if (Object.keys(cookies).length > 0) {
      // Handle server-side cookies if needed
      console.log('Server cookies received:', cookies);
    }
  }, [cookies]);

  // Auto-refresh token when it's about to expire
  useEffect(() => {
    if (!auth.isAuthenticated || !auth.token) return;

    // Set up token refresh interval (refresh 5 minutes before expiry)
    const refreshInterval = setInterval(() => {
      auth.refreshAuth().catch((error) => {
        console.error('Auto token refresh failed:', error);
        // If refresh fails, user will be logged out automatically
      });
    }, 25 * 60 * 1000); // 25 minutes

    return () => clearInterval(refreshInterval);
  }, [auth.isAuthenticated, auth.token, auth.refreshAuth]);

  const contextValue: AuthContextType = {
    // State
    user: auth.user,
    token: auth.token,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    isInitializing: auth.isInitializing,
    userPermissions: auth.userPermissions,
    userRoles: auth.userRoles,
    deviceUuid: auth.deviceUuid,
    
    // Actions
    login: auth.login,
    register: auth.register,
    logout: auth.logout,
    refreshAuth: auth.refreshAuth,
    getProfile: auth.getProfile,
    
    // Permission helpers
    hasPermission: auth.hasPermission,
    hasAnyPermission: auth.hasAnyPermission,
    hasAllPermissions: auth.hasAllPermissions,
    hasRole: auth.hasRole,
    isAdmin: auth.isAdmin,
    isSeller: auth.isSeller,
    isCustomer: auth.isCustomer,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use Auth Context
 * Alternative to useAuth hook
 */
export const useAuthContext = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  
  return context;
};

/**
 * HOC for components that need authentication
 */
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredPermissions?: string[];
    requiredRoles?: string[];
    requireAll?: boolean;
    fallback?: React.ComponentType;
  }
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const auth = useAuthContext();
    const {
      requiredPermissions = [],
      requiredRoles = [],
      requireAll = false,
      fallback: Fallback,
    } = options || {};

    // Show loading during initialization
    if (auth.isInitializing) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      );
    }

    // Check authentication
    if (!auth.isAuthenticated) {
      if (Fallback) {
        return <Fallback />;
      }
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
            <p className="text-muted-foreground">Please log in to access this page.</p>
          </div>
        </div>
      );
    }

    // Check permissions
    if (requiredPermissions.length > 0) {
      const hasPermissions = requireAll 
        ? auth.hasAllPermissions(requiredPermissions)
        : auth.hasAnyPermission(requiredPermissions);
      
      if (!hasPermissions) {
        if (Fallback) {
          return <Fallback />;
        }
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have permission to access this page.</p>
            </div>
          </div>
        );
      }
    }

    // Check roles
    if (requiredRoles.length > 0) {
      const hasRequiredRole = requireAll
        ? requiredRoles.every(role => auth.hasRole(role))
        : requiredRoles.some(role => auth.hasRole(role));
      
      if (!hasRequiredRole) {
        if (Fallback) {
          return <Fallback />;
        }
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground">You don't have the required role to access this page.</p>
            </div>
          </div>
        );
      }
    }

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * Auth Guard Component
 * Declarative way to protect content
 */
interface AuthGuardProps {
  children: ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
  loading?: ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallback,
  loading,
}) => {
  const auth = useAuthContext();

  // Show loading during initialization
  if (auth.isInitializing) {
    return loading || (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check authentication
  if (!auth.isAuthenticated) {
    return fallback || (
      <div className="text-center p-4">
        <p className="text-muted-foreground">Please log in to view this content.</p>
      </div>
    );
  }

  // Check permissions
  if (requiredPermissions.length > 0) {
    const hasPermissions = requireAll 
      ? auth.hasAllPermissions(requiredPermissions)
      : auth.hasAnyPermission(requiredPermissions);
    
    if (!hasPermissions) {
      return fallback || (
        <div className="text-center p-4">
          <p className="text-muted-foreground">You don't have permission to view this content.</p>
        </div>
      );
    }
  }

  // Check roles
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requireAll
      ? requiredRoles.every(role => auth.hasRole(role))
      : requiredRoles.some(role => auth.hasRole(role));
    
    if (!hasRequiredRole) {
      return fallback || (
        <div className="text-center p-4">
          <p className="text-muted-foreground">You don't have the required role to view this content.</p>
        </div>
      );
    }
  }

  return <>{children}</>;
};
