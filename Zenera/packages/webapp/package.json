{"name": "@zenera/webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@tanstack/react-query": "^5.0.0", "@zenera/sharing": "workspace:*", "@zenera/ui-components": "workspace:*", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "dayjs": "^1.11.13", "i18next": "^23.7.0", "i18next-browser-languagedetector": "^7.2.0", "js-cookie": "^3.0.5", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.47.0", "react-i18next": "^13.5.0", "tailwind-merge": "^2.0.0", "uuid": "^11.1.0", "zod": "^3.22.0", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.0", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "^5"}}